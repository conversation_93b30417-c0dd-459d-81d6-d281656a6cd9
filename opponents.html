<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对手管理</title>
    <link rel="stylesheet" href="ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .search-section {
            padding: var(--spacing-md);
            background: var(--background-color);
        }

        .search-container {
            background: white;
            border-radius: var(--radius-lg);
            border: 1px solid var(--ios-gray4);
            display: flex;
            align-items: center;
            padding: var(--spacing-sm) var(--spacing-md);
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            padding: var(--spacing-sm) 0;
            background: transparent;
        }

        .search-icon {
            color: var(--ios-gray);
            margin-right: var(--spacing-sm);
        }

        .opponents-list {
            padding: var(--spacing-md);
        }

        .opponent-item {
            background: white;
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
            cursor: pointer;
            transition: all 0.2s;
        }

        .opponent-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .opponent-header {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-md);
        }

        .opponent-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--ios-blue), var(--ios-purple));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: 600;
            margin-right: var(--spacing-md);
        }

        .opponent-info {
            flex: 1;
        }

        .opponent-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .opponent-level {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            background: var(--ios-gray6);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .opponent-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .stat-number.total { color: var(--text-primary); }
        .stat-number.wins { color: var(--ios-green); }
        .stat-number.losses { color: var(--ios-red); }
        .stat-number.draws { color: var(--ios-orange); }

        .opponent-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .opponent-style {
            font-size: 14px;
            color: var(--text-secondary);
            font-style: italic;
        }

        .win-rate-badge {
            background: var(--ios-green);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .win-rate-badge.low {
            background: var(--ios-red);
        }

        .win-rate-badge.medium {
            background: var(--ios-orange);
        }

        .floating-add {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: var(--ios-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-lg);
            cursor: pointer;
            transition: all 0.2s;
            z-index: 50;
        }

        .floating-add:hover {
            transform: scale(1.1);
        }

        .floating-add i {
            color: white;
            font-size: 24px;
        }

        .empty-state {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--text-secondary);
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: var(--spacing-md);
            opacity: 0.3;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .empty-subtitle {
            font-size: 14px;
            margin-bottom: var(--spacing-lg);
        }

        .sort-options {
            display: flex;
            gap: var(--spacing-sm);
            padding: 0 var(--spacing-md) var(--spacing-md);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .sort-options::-webkit-scrollbar {
            display: none;
        }

        .sort-option {
            background: white;
            border: 1px solid var(--ios-gray4);
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
        }

        .sort-option.active {
            background: var(--ios-blue);
            color: white;
            border-color: var(--ios-blue);
        }

        .recent-games {
            margin-top: var(--spacing-sm);
            padding-top: var(--spacing-sm);
            border-top: 0.5px solid var(--ios-gray5);
        }

        .recent-game {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-xs) 0;
            font-size: 12px;
        }

        .game-info {
            color: var(--text-secondary);
        }

        .game-result {
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 600;
            color: white;
        }

        .game-result.win { background: var(--ios-green); }
        .game-result.lose { background: var(--ios-red); }
        .game-result.draw { background: var(--ios-orange); }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                    </div>
                    <div class="wifi-icon"></div>
                    <span>100%</span>
                    <div class="battery-icon"></div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <button class="nav-button" onclick="Navigator.back()">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">对手管理</div>
                <button class="nav-button" onclick="showSortOptions()">
                    <i class="fas fa-sort"></i>
                </button>
            </div>

            <!-- 搜索栏 -->
            <div class="search-section">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="搜索对手姓名..." id="searchInput">
                </div>
            </div>

            <!-- 排序选项 -->
            <div class="sort-options" id="sortOptions">
                <div class="sort-option active" data-sort="name">按姓名</div>
                <div class="sort-option" data-sort="games">按对局数</div>
                <div class="sort-option" data-sort="winrate">按胜率</div>
                <div class="sort-option" data-sort="recent">按最近对局</div>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <div class="opponents-list" id="opponentsList">
                    <!-- 动态加载对手列表 -->
                </div>
            </div>

            <!-- 悬浮添加按钮 -->
            <div class="floating-add" onclick="Navigator.navigate('add-opponent.html')">
                <i class="fas fa-plus"></i>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="app-utils.js"></script>
    <script>
        let searchQuery = '';
        let currentSort = 'name';

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSearch();
            initSortOptions();
            generateOpponentsFromRecords();
            loadOpponents();
        });

        // 初始化搜索
        function initSearch() {
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    searchQuery = this.value.trim().toLowerCase();
                    loadOpponents();
                }, 300);
            });
        }

        // 初始化排序选项
        function initSortOptions() {
            const options = document.querySelectorAll('.sort-option');
            options.forEach(option => {
                option.addEventListener('click', function() {
                    options.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                    currentSort = this.dataset.sort;
                    loadOpponents();
                });
            });
        }

        // 从对局记录生成对手信息
        function generateOpponentsFromRecords() {
            const records = appStorage.get('gameRecords') || [];
            const existingOpponents = appStorage.get('opponents') || [];
            const opponentMap = new Map();

            // 统计现有对手
            existingOpponents.forEach(opponent => {
                opponentMap.set(opponent.name, opponent);
            });

            // 从记录中提取对手信息
            records.forEach(record => {
                const opponentName = record.opponent;
                if (!opponentMap.has(opponentName)) {
                    opponentMap.set(opponentName, {
                        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
                        name: opponentName,
                        avatar: opponentName.charAt(0).toUpperCase(),
                        level: '未知',
                        style: '待分析',
                        strengths: '',
                        weaknesses: '',
                        totalGames: 0,
                        wins: 0,
                        losses: 0,
                        draws: 0,
                        notes: '',
                        createdAt: new Date().toISOString()
                    });
                }
            });

            // 更新对局统计
            opponentMap.forEach(opponent => {
                const opponentRecords = records.filter(r => r.opponent === opponent.name);
                opponent.totalGames = opponentRecords.length;
                opponent.wins = opponentRecords.filter(r => r.result === '胜').length;
                opponent.losses = opponentRecords.filter(r => r.result === '负').length;
                opponent.draws = opponentRecords.filter(r => r.result === '和').length;
                
                // 添加最近对局信息
                opponent.recentGames = opponentRecords
                    .sort((a, b) => new Date(b.date) - new Date(a.date))
                    .slice(0, 3);
            });

            // 保存更新后的对手信息
            appStorage.set('opponents', Array.from(opponentMap.values()));
        }

        // 加载对手列表
        function loadOpponents() {
            // 确保对手数据是最新的
            appStorage.updateOpponentsFromRecords();

            let opponents = appStorage.get('opponents') || [];

            // 搜索筛选
            if (searchQuery) {
                opponents = opponents.filter(opponent =>
                    opponent.name.toLowerCase().includes(searchQuery) ||
                    (opponent.style && opponent.style.toLowerCase().includes(searchQuery)) ||
                    (opponent.notes && opponent.notes.toLowerCase().includes(searchQuery)) ||
                    (opponent.level && opponent.level.toLowerCase().includes(searchQuery))
                );
            }

            // 排序
            opponents.sort((a, b) => {
                switch (currentSort) {
                    case 'name':
                        return a.name.localeCompare(b.name);
                    case 'games':
                        return b.totalGames - a.totalGames;
                    case 'winrate':
                        const aWinRate = a.totalGames > 0 ? a.wins / a.totalGames : 0;
                        const bWinRate = b.totalGames > 0 ? b.wins / b.totalGames : 0;
                        return bWinRate - aWinRate;
                    case 'recent':
                        const aLastGame = a.recentGames && a.recentGames.length > 0 ? new Date(a.recentGames[0].date) : new Date(0);
                        const bLastGame = b.recentGames && b.recentGames.length > 0 ? new Date(b.recentGames[0].date) : new Date(0);
                        return bLastGame - aLastGame;
                    default:
                        return 0;
                }
            });

            renderOpponents(opponents);
        }

        // 渲染对手列表
        function renderOpponents(opponents) {
            const container = document.getElementById('opponentsList');

            if (opponents.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="empty-title">暂无对手信息</div>
                        <div class="empty-subtitle">
                            ${searchQuery ? '没有找到匹配的对手' : '开始记录对局后会自动生成对手信息'}
                        </div>
                        ${!searchQuery ? `
                            <button class="button-primary" onclick="Navigator.navigate('add-opponent.html')">
                                <i class="fas fa-plus"></i> 添加对手信息
                            </button>
                        ` : ''}
                    </div>
                `;
                return;
            }

            container.innerHTML = opponents.map(opponent => {
                const winRate = opponent.totalGames > 0 ? (opponent.wins / opponent.totalGames * 100) : 0;
                const winRateClass = winRate >= 60 ? 'high' : winRate >= 40 ? 'medium' : 'low';
                
                return `
                    <div class="opponent-item" onclick="viewOpponentDetail('${opponent.id}')">
                        <div class="opponent-header">
                            <div class="opponent-avatar">${opponent.avatar}</div>
                            <div class="opponent-info">
                                <div class="opponent-name">${opponent.name}</div>
                                <div class="opponent-level">
                                    <i class="fas fa-star"></i>
                                    <span>${opponent.level}</span>
                                </div>
                            </div>
                            <div class="win-rate-badge ${winRateClass}">
                                ${winRate.toFixed(1)}%
                            </div>
                        </div>
                        
                        <div class="opponent-stats">
                            <div class="stat-item">
                                <div class="stat-number total">${opponent.totalGames}</div>
                                <div class="stat-label">总局</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number wins">${opponent.wins}</div>
                                <div class="stat-label">胜</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number losses">${opponent.losses}</div>
                                <div class="stat-label">负</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number draws">${opponent.draws}</div>
                                <div class="stat-label">和</div>
                            </div>
                        </div>

                        <div class="opponent-details">
                            <div class="opponent-style">${opponent.style}</div>
                        </div>

                        ${opponent.recentGames && opponent.recentGames.length > 0 ? `
                            <div class="recent-games">
                                ${opponent.recentGames.map(game => `
                                    <div class="recent-game">
                                        <div class="game-info">
                                            ${game.gameType} · ${DateUtils.getRelativeTime(game.date)}
                                        </div>
                                        <div class="game-result ${game.result === '胜' ? 'win' : game.result === '负' ? 'lose' : 'draw'}">
                                            ${game.result}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                `;
            }).join('');
        }

        // 查看对手详情
        function viewOpponentDetail(opponentId) {
            Navigator.navigate('opponent-detail.html', { id: opponentId });
        }

        // 显示排序选项
        function showSortOptions() {
            const options = [
                {
                    text: '按姓名排序',
                    icon: 'fas fa-sort-alpha-down',
                    action: () => setSortOption('name')
                },
                {
                    text: '按对局数排序',
                    icon: 'fas fa-sort-numeric-down',
                    action: () => setSortOption('games')
                },
                {
                    text: '按胜率排序',
                    icon: 'fas fa-percentage',
                    action: () => setSortOption('winrate')
                },
                {
                    text: '按最近对局排序',
                    icon: 'fas fa-clock',
                    action: () => setSortOption('recent')
                }
            ];

            UIUtils.showActionSheet('排序方式', options);
        }

        // 设置排序选项
        function setSortOption(sortType) {
            const options = document.querySelectorAll('.sort-option');
            options.forEach(opt => opt.classList.remove('active'));

            const targetOption = document.querySelector(`[data-sort="${sortType}"]`);
            if (targetOption) {
                targetOption.classList.add('active');
            }

            currentSort = sortType;
            loadOpponents();
            UIUtils.showToast('排序已更新', 'success');
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                Navigator.back();
            } else if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                document.getElementById('searchInput').focus();
            } else if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                Navigator.navigate('add-opponent.html');
            }
        });
    </script>
</body>
</html>
