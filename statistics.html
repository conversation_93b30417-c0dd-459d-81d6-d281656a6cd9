<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据统计</title>
    <link rel="stylesheet" href="ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .stats-overview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin: var(--spacing-md);
        }

        .overview-card {
            background: white;
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
            box-shadow: var(--shadow-md);
            border: 0.5px solid var(--ios-gray5);
        }

        .overview-number {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: var(--spacing-xs);
        }

        .overview-label {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .overview-card:nth-child(1) .overview-number { color: var(--primary-color); }
        .overview-card:nth-child(2) .overview-number { color: var(--ios-green); }
        .overview-card:nth-child(3) .overview-number { color: var(--ios-red); }
        .overview-card:nth-child(4) .overview-number { color: var(--ios-orange); }

        .chart-section {
            background: white;
            margin: var(--spacing-md);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
        }

        .section-header {
            padding: var(--spacing-md);
            border-bottom: 0.5px solid var(--ios-gray5);
            background: var(--ios-gray6);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .chart-container {
            padding: var(--spacing-md);
            height: 200px;
            position: relative;
        }

        .win-rate-chart {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        .circular-progress {
            position: relative;
            width: 120px;
            height: 120px;
        }

        .progress-ring {
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            stroke: var(--ios-gray5);
            fill: transparent;
            stroke-width: 8;
        }

        .progress-ring-progress {
            stroke: var(--ios-green);
            fill: transparent;
            stroke-width: 8;
            stroke-linecap: round;
            transition: stroke-dasharray 0.5s ease-in-out;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .progress-percentage {
            font-size: 24px;
            font-weight: 700;
            color: var(--ios-green);
        }

        .progress-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .game-type-stats {
            background: white;
            margin: var(--spacing-md);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
        }

        .game-stat-item {
            display: flex;
            align-items: center;
            padding: var(--spacing-md);
            border-bottom: 0.5px solid var(--ios-gray5);
        }

        .game-stat-item:last-child {
            border-bottom: none;
        }

        .game-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: var(--spacing-md);
        }

        .game-stat-info {
            flex: 1;
        }

        .game-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .game-record {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .game-win-rate {
            font-size: 18px;
            font-weight: 700;
            color: var(--ios-green);
        }

        .trend-container {
            padding: var(--spacing-md);
            background: white;
        }

        .recent-performance {
            background: white;
            margin: var(--spacing-md);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
        }

        .performance-item {
            display: flex;
            align-items: center;
            padding: var(--spacing-md);
            border-bottom: 0.5px solid var(--ios-gray5);
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .performance-item:hover {
            background: var(--ios-gray6);
        }

        .performance-item:last-child {
            border-bottom: none;
        }

        .performance-date {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }

        .performance-game {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .performance-result {
            margin-left: auto;
            text-align: right;
            min-width: 40px;
        }

        .performance-result .result-text {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .performance-result.win .result-text { color: var(--ios-green); }
        .performance-result.lose .result-text { color: var(--ios-red); }
        .performance-result.draw .result-text { color: var(--ios-orange); }

        .empty-state {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--text-secondary);
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: var(--spacing-md);
            opacity: 0.3;
        }

        .time-filter {
            display: flex;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .time-filter::-webkit-scrollbar {
            display: none;
        }

        .time-filter-item {
            background: white;
            border: 1px solid var(--ios-gray4);
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
        }

        .time-filter-item.active {
            background: var(--ios-blue);
            color: white;
            border-color: var(--ios-blue);
        }

        /* 新增样式 */
        .trend-summary {
            background: #f8f9fa;
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
        }

        .trend-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid #dee2e6;
        }

        .trend-stat-item {
            text-align: center;
            flex: 1;
        }

        .stat-label {
            font-size: 11px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--ios-blue);
        }

        .trend-indicator {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            padding: var(--spacing-sm);
            background: white;
            border-radius: var(--radius-sm);
        }

        .trend-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .trend-icon.rising {
            background: #d4edda;
            color: #155724;
        }

        .trend-icon.falling {
            background: #f8d7da;
            color: #721c24;
        }

        .trend-icon.stable {
            background: #d1ecf1;
            color: #0c5460;
        }

        .trend-text {
            flex: 1;
        }

        .trend-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .trend-period {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .trend-months {
            display: flex;
            gap: var(--spacing-xs);
            overflow-x: auto;
            padding: var(--spacing-xs) 0;
        }

        .month-item {
            flex: 1;
            min-width: 60px;
            text-align: center;
            background: white;
            border-radius: var(--radius-sm);
            padding: var(--spacing-sm);
        }

        .month-name {
            font-size: 11px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .month-rate {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .month-rate.good { color: var(--ios-green); }
        .month-rate.normal { color: var(--ios-blue); }
        .month-rate.poor { color: var(--ios-red); }

        .month-games {
            font-size: 10px;
            color: var(--text-secondary);
        }



        .trend-empty {
            text-align: center;
            padding: var(--spacing-xl);
            background: #f8f9fa;
            border-radius: var(--radius-md);
            border: 2px dashed #dee2e6;
        }

        .trend-empty .empty-icon {
            font-size: 32px;
            color: #adb5bd;
            margin-bottom: var(--spacing-sm);
        }

        .trend-empty .empty-text {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }

        .trend-empty .empty-hint {
            font-size: 12px;
            color: #adb5bd;
        }

        .win-rate-bar {
            width: 60px;
            height: 4px;
            background: var(--ios-gray5);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 4px;
        }

        .win-rate-fill {
            height: 100%;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .game-total {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .performance-summary {
            display: flex;
            padding: var(--spacing-md);
            background: var(--ios-gray6);
            border-bottom: 0.5px solid var(--ios-gray5);
        }

        .summary-item {
            flex: 1;
            text-align: center;
        }

        .summary-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .summary-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .summary-value.good {
            color: var(--ios-green);
        }

        .summary-value.poor {
            color: var(--ios-red);
        }

        .performance-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .performance-info {
            flex: 1;
        }

        .performance-game {
            margin: 4px 0;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 4px;
        }

        .game-type {
            font-weight: 600;
            color: var(--text-primary);
            background: var(--ios-gray6);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }

        .vs {
            color: var(--text-secondary);
            font-size: 12px;
        }

        .opponent {
            color: var(--ios-blue);
            font-weight: 500;
        }

        .performance-location {
            font-size: 11px;
            color: var(--text-secondary);
            margin-top: 2px;
        }

        .importance {
            font-size: 10px;
            color: #ff9500;
            margin-top: 2px;
            letter-spacing: 1px;
        }

        .view-more {
            padding: var(--spacing-md);
            text-align: center;
            color: var(--ios-blue);
            font-size: 14px;
            cursor: pointer;
            border-top: 0.5px solid var(--ios-gray5);
        }

        .view-more:hover {
            background: var(--ios-gray6);
        }

    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                    </div>
                    <div class="wifi-icon"></div>
                    <span>100%</span>
                    <div class="battery-icon"></div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <div class="nav-title">数据统计</div>

            </div>

            <!-- 时间筛选 -->
            <div class="time-filter" id="timeFilter">
                <div class="time-filter-item active" data-period="all">全部</div>
                <div class="time-filter-item" data-period="week">本周</div>
                <div class="time-filter-item" data-period="month">本月</div>
                <div class="time-filter-item" data-period="quarter">本季度</div>
                <div class="time-filter-item" data-period="year">今年</div>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <!-- 总览统计 -->
                <div class="stats-overview" id="statsOverview">
                    <!-- 动态加载总览数据 -->
                </div>

                <!-- 胜率图表 -->
                <div class="chart-section">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-chart-pie"></i>
                            总体胜率
                        </div>
                    </div>
                    <div class="chart-container">
                        <div class="win-rate-chart">
                            <div class="circular-progress">
                                <svg class="progress-ring" width="120" height="120">
                                    <circle class="progress-ring-circle" cx="60" cy="60" r="52"></circle>
                                    <circle class="progress-ring-progress" cx="60" cy="60" r="52" id="progressCircle"></circle>
                                </svg>
                                <div class="progress-text">
                                    <div class="progress-percentage" id="winRatePercentage">0%</div>
                                    <div class="progress-label">胜率</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 游戏类型统计 -->
                <div class="game-type-stats">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-gamepad"></i>
                            游戏类型统计
                        </div>
                    </div>
                    <div id="gameTypeStats">
                        <!-- 动态加载游戏类型统计 -->
                    </div>
                </div>

                <!-- 趋势图表 -->
                <div class="chart-section">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-chart-line"></i>
                            胜率趋势
                        </div>
                    </div>
                    <div class="trend-container" id="trendPoints">
                        <!-- 动态生成趋势图表 -->
                    </div>
                </div>

                <!-- 最近表现 -->
                <div class="recent-performance">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-history"></i>
                            最近表现
                        </div>
                    </div>
                    <div id="recentPerformance">
                        <!-- 动态加载最近表现 -->
                    </div>
                </div>


            </div>

            <!-- 底部标签栏 -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item" data-tab="home">
                    <div class="tab-icon"><i class="fas fa-home"></i></div>
                    <div class="tab-label">首页</div>
                </a>
                <a href="records.html" class="tab-item" data-tab="records">
                    <div class="tab-icon"><i class="fas fa-list"></i></div>
                    <div class="tab-label">记录</div>
                </a>
                <a href="statistics.html" class="tab-item active" data-tab="statistics">
                    <div class="tab-icon"><i class="fas fa-chart-bar"></i></div>
                    <div class="tab-label">统计</div>
                </a>
                <a href="knowledge.html" class="tab-item" data-tab="knowledge">
                    <div class="tab-icon"><i class="fas fa-book"></i></div>
                    <div class="tab-label">技巧</div>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="app-utils.js"></script>
    <script>
        let currentPeriod = 'all';

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initTimeFilter();
            loadStatistics();
        });

        // 初始化时间筛选
        function initTimeFilter() {
            const filters = document.querySelectorAll('.time-filter-item');
            filters.forEach(filter => {
                filter.addEventListener('click', function() {
                    filters.forEach(f => f.classList.remove('active'));
                    this.classList.add('active');
                    currentPeriod = this.dataset.period;
                    loadStatistics();
                });
            });
        }

        // 加载统计数据
        function loadStatistics() {
            const records = getFilteredRecords();

            if (records.length === 0) {
                showEmptyState();
                return;
            }

            // 使用新的统计数据
            const stats = appStorage.get('statistics') || {};

            loadOverviewStats(records, stats);
            loadWinRateChart(records);
            loadGameTypeStats(stats.byGameType || {});
            loadTrendChart(stats.byMonth || {});
            loadRecentPerformance(records);
            loadOpponentStats(stats.byOpponent || {});
        }

        // 获取筛选后的记录
        function getFilteredRecords() {
            const allRecords = appStorage.get('gameRecords') || [];
            
            if (currentPeriod === 'all') {
                return allRecords;
            }

            const now = new Date();
            let startDate;

            switch (currentPeriod) {
                case 'week':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case 'month':
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    break;
                case 'quarter':
                    const quarter = Math.floor(now.getMonth() / 3);
                    startDate = new Date(now.getFullYear(), quarter * 3, 1);
                    break;
                case 'year':
                    startDate = new Date(now.getFullYear(), 0, 1);
                    break;
                default:
                    return allRecords;
            }

            return allRecords.filter(record => new Date(record.date) >= startDate);
        }

        // 加载总览统计
        function loadOverviewStats(records) {
            const totalGames = records.length;
            const wins = records.filter(r => r.result === '胜').length;
            const losses = records.filter(r => r.result === '负').length;
            const draws = records.filter(r => r.result === '和').length;

            const container = document.getElementById('statsOverview');
            container.innerHTML = `
                <div class="overview-card">
                    <div class="overview-number">${totalGames}</div>
                    <div class="overview-label">总对局</div>
                </div>
                <div class="overview-card">
                    <div class="overview-number">${wins}</div>
                    <div class="overview-label">胜局</div>
                </div>
                <div class="overview-card">
                    <div class="overview-number">${losses}</div>
                    <div class="overview-label">负局</div>
                </div>
                <div class="overview-card">
                    <div class="overview-number">${draws}</div>
                    <div class="overview-label">和局</div>
                </div>
            `;
        }

        // 加载胜率图表
        function loadWinRateChart(records) {
            const totalGames = records.length;
            const wins = records.filter(r => r.result === '胜').length;
            const winRate = totalGames > 0 ? (wins / totalGames * 100) : 0;

            // 更新圆形进度条
            const circle = document.getElementById('progressCircle');
            const radius = 52;
            const circumference = 2 * Math.PI * radius;
            const offset = circumference - (winRate / 100) * circumference;

            circle.style.strokeDasharray = circumference;
            circle.style.strokeDashoffset = offset;

            // 更新百分比显示
            document.getElementById('winRatePercentage').textContent = winRate.toFixed(1) + '%';
        }

        // 加载游戏类型统计
        function loadGameTypeStats(gameTypeStats) {
            const container = document.getElementById('gameTypeStats');

            if (!gameTypeStats || Object.keys(gameTypeStats).length === 0) {
                container.innerHTML = '<div class="empty-state"><div class="empty-icon"><i class="fas fa-gamepad"></i></div><div>暂无游戏类型数据</div></div>';
                return;
            }

            // 按总对局数排序
            const sortedGameTypes = Object.entries(gameTypeStats)
                .sort((a, b) => b[1].totalGames - a[1].totalGames);

            container.innerHTML = sortedGameTypes.map(([gameType, stats]) => {
                const gameConfig = GAME_TYPES[gameType] || GAME_TYPES['其他'];
                const winRate = parseFloat(stats.winRate) || 0;

                return `
                    <div class="game-stat-item" onclick="Navigator.navigate('records.html', {gameType: '${gameType}'})">
                        <div class="game-icon" style="background: ${gameConfig.color}20; color: ${gameConfig.color};">
                            ${gameConfig.icon}
                        </div>
                        <div class="game-stat-info">
                            <div class="game-name">${gameType}</div>
                            <div class="game-record">${stats.wins}胜 ${stats.losses}负 ${stats.draws}和</div>
                            <div class="game-total">${stats.totalGames}局</div>
                        </div>
                        <div class="game-win-rate">
                            <div class="win-rate-number">${winRate}%</div>
                            <div class="win-rate-bar">
                                <div class="win-rate-fill" style="width: ${winRate}%; background: ${gameConfig.color};"></div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 加载趋势图表
        function loadTrendChart(monthlyStats) {
            const container = document.getElementById('trendPoints');

            if (!monthlyStats || Object.keys(monthlyStats).length === 0) {
                container.innerHTML = `
                    <div class="trend-empty">
                        <div class="empty-icon"><i class="fas fa-chart-line"></i></div>
                        <div class="empty-text">暂无趋势数据</div>
                        <div class="empty-hint">记录更多对局后查看胜率趋势</div>
                    </div>
                `;
                return;
            }

            // 获取最近6个月的数据
            const months = Object.keys(monthlyStats).sort().slice(-6);

            if (months.length === 0) {
                container.innerHTML = `
                    <div class="trend-empty">
                        <div class="empty-icon"><i class="fas fa-chart-line"></i></div>
                        <div class="empty-text">暂无趋势数据</div>
                        <div class="empty-hint">记录更多对局后查看胜率趋势</div>
                    </div>
                `;
                return;
            }

            // 计算趋势分析
            const firstRate = parseFloat(monthlyStats[months[0]].winRate) || 0;
            const lastRate = parseFloat(monthlyStats[months[months.length - 1]].winRate) || 0;
            const change = lastRate - firstRate;

            let trendText = '';
            let trendClass = '';
            let trendIcon = '';

            if (Math.abs(change) < 5) {
                trendText = '胜率保持稳定';
                trendClass = 'stable';
                trendIcon = 'fas fa-minus';
            } else if (change > 0) {
                trendText = `胜率上升 ${change.toFixed(1)}%`;
                trendClass = 'rising';
                trendIcon = 'fas fa-arrow-up';
            } else {
                trendText = `胜率下降 ${Math.abs(change).toFixed(1)}%`;
                trendClass = 'falling';
                trendIcon = 'fas fa-arrow-down';
            }

            // 简化的卡片式显示
            container.innerHTML = `
                <div class="trend-summary">
                    <div class="trend-stats">
                        <div class="trend-stat-item">
                            <div class="stat-label">最高胜率</div>
                            <div class="stat-value">${Math.max(...months.map(m => parseFloat(monthlyStats[m].winRate) || 0)).toFixed(1)}%</div>
                        </div>
                        <div class="trend-stat-item">
                            <div class="stat-label">最低胜率</div>
                            <div class="stat-value">${Math.min(...months.map(m => parseFloat(monthlyStats[m].winRate) || 0)).toFixed(1)}%</div>
                        </div>
                        <div class="trend-stat-item">
                            <div class="stat-label">平均胜率</div>
                            <div class="stat-value">${(months.reduce((sum, m) => sum + (parseFloat(monthlyStats[m].winRate) || 0), 0) / months.length).toFixed(1)}%</div>
                        </div>
                    </div>

                    <div class="trend-indicator">
                        <div class="trend-icon ${trendClass}">
                            <i class="${trendIcon}"></i>
                        </div>
                        <div class="trend-text">
                            <div class="trend-title">${trendText}</div>
                            <div class="trend-period">最近${months.length}个月</div>
                        </div>
                    </div>

                    <div class="trend-months">
                        ${months.map(month => {
                            const stats = monthlyStats[month];
                            const winRate = parseFloat(stats.winRate) || 0;
                            const monthName = month.split('-')[1] + '月';

                            return `
                                <div class="month-item">
                                    <div class="month-name">${monthName}</div>
                                    <div class="month-rate ${winRate >= 60 ? 'good' : winRate >= 40 ? 'normal' : 'poor'}">${winRate}%</div>
                                    <div class="month-games">${stats.totalGames}局</div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>
            `;
        }





        // 加载对手统计
        function loadOpponentStats(opponentStats) {
            const container = document.getElementById('opponentStats');
            if (!container) return;

            const opponents = Object.entries(opponentStats)
                .sort((a, b) => b[1].totalGames - a[1].totalGames)
                .slice(0, 5);

            if (opponents.length === 0) {
                container.innerHTML = '<div class="empty-state"><div class="empty-icon"><i class="fas fa-users"></i></div><div>暂无对手数据</div></div>';
                return;
            }

            container.innerHTML = opponents.map(([name, stats]) => `
                <div class="opponent-stat-item" onclick="Navigator.navigate('records.html', {opponent: '${name}'})">
                    <div class="opponent-info">
                        <div class="opponent-name">${name}</div>
                        <div class="opponent-games">${stats.totalGames}局</div>
                    </div>
                    <div class="opponent-record">
                        <span class="win-count">${stats.wins}胜</span>
                        <span class="lose-count">${stats.losses}负</span>
                        <span class="draw-count">${stats.draws}和</span>
                    </div>
                    <div class="opponent-winrate">${stats.winRate}%</div>
                </div>
            `).join('');
        }

        // 加载最近表现
        function loadRecentPerformance(records) {
            const container = document.getElementById('recentPerformance');

            if (!records || records.length === 0) {
                container.innerHTML = '<div class="empty-state"><div class="empty-icon"><i class="fas fa-history"></i></div><div>暂无最近记录</div></div>';
                return;
            }

            // 按日期排序，取最近10条记录
            const recentRecords = records
                .sort((a, b) => new Date(b.date) - new Date(a.date))
                .slice(0, 10);

            // 计算最近表现趋势
            const recentWins = recentRecords.filter(r => r.result === '胜').length;
            const recentWinRate = recentRecords.length > 0 ? (recentWins / recentRecords.length * 100).toFixed(1) : 0;

            container.innerHTML = `
                <div class="performance-summary">
                    <div class="summary-item">
                        <div class="summary-label">最近${recentRecords.length}局</div>
                        <div class="summary-value">${recentWins}胜 ${recentRecords.filter(r => r.result === '负').length}负 ${recentRecords.filter(r => r.result === '和').length}和</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">近期胜率</div>
                        <div class="summary-value ${recentWinRate >= 60 ? 'good' : recentWinRate >= 40 ? 'normal' : 'poor'}">${recentWinRate}%</div>
                    </div>
                </div>
                <div class="performance-list">
                    ${recentRecords.slice(0, 5).map(record => `
                        <div class="performance-item" onclick="Navigator.navigate('record-detail.html', {id: '${record.id}'})">
                            <div class="performance-info">
                                <div class="performance-date">${DateUtils.formatDate(record.date, 'MM-DD HH:mm')}</div>
                                <div class="performance-game">
                                    <span class="game-type">${record.gameType}</span>
                                    <span class="vs">vs</span>
                                    <span class="opponent">${record.opponent}</span>
                                </div>
                                ${record.location ? `<div class="performance-location">${record.location}</div>` : ''}
                            </div>
                            <div class="performance-result ${record.result === '胜' ? 'win' : record.result === '负' ? 'lose' : 'draw'}">
                                <div class="result-text">${record.result}</div>
                                ${record.importance ? `<div class="importance">${'★'.repeat(record.importance)}</div>` : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
                ${recentRecords.length > 5 ? `
                    <div class="view-more" onclick="Navigator.navigate('records.html')">
                        <i class="fas fa-chevron-right"></i> 查看更多记录
                    </div>
                ` : ''}
            `;
        }

        // 显示空状态
        function showEmptyState() {
            const containers = ['statsOverview', 'gameTypeStats', 'recentPerformance'];
            containers.forEach(id => {
                const container = document.getElementById(id);
                container.innerHTML = '<div class="empty-state"><div class="empty-icon"><i class="fas fa-chart-bar"></i></div><div>暂无数据</div></div>';
            });

            document.getElementById('winRatePercentage').textContent = '0%';
            document.getElementById('progressCircle').style.strokeDashoffset = '327';
            document.getElementById('trendPoints').innerHTML = '';
        }











        // 复制到剪贴板
        function copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    UIUtils.showToast('内容已复制到剪贴板', 'success');
                });
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();

                try {
                    document.execCommand('copy');
                    UIUtils.showToast('内容已复制到剪贴板', 'success');
                } catch (err) {
                    UIUtils.showToast('复制失败', 'error');
                }

                document.body.removeChild(textArea);
            }
        }
    </script>
</body>
</html>
