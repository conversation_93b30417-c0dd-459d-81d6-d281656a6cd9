<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索</title>
    <link rel="stylesheet" href="ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .search-header {
            padding: var(--spacing-md);
            background: var(--background-color);
            border-bottom: 0.5px solid var(--ios-gray5);
        }

        .search-container {
            background: white;
            border-radius: var(--radius-lg);
            border: 1px solid var(--ios-gray4);
            display: flex;
            align-items: center;
            padding: var(--spacing-sm) var(--spacing-md);
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 17px;
            padding: var(--spacing-sm) 0;
            background: transparent;
        }

        .search-icon {
            color: var(--ios-gray);
            margin-right: var(--spacing-sm);
        }

        .clear-button {
            background: var(--ios-gray);
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            margin-left: var(--spacing-sm);
        }

        .filter-section {
            background: white;
            margin: var(--spacing-md);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
        }

        .filter-header {
            padding: var(--spacing-md);
            border-bottom: 0.5px solid var(--ios-gray5);
            background: var(--ios-gray6);
        }

        .filter-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .filter-content {
            padding: var(--spacing-md);
        }

        .filter-group {
            margin-bottom: var(--spacing-lg);
        }

        .filter-group:last-child {
            margin-bottom: 0;
        }

        .filter-label {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .filter-options {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
        }

        .filter-option {
            background: var(--ios-gray6);
            border: 1px solid var(--ios-gray4);
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-option.active {
            background: var(--ios-blue);
            color: white;
            border-color: var(--ios-blue);
        }

        .date-range {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }

        .date-input {
            flex: 1;
            padding: var(--spacing-sm);
            border: 1px solid var(--ios-gray4);
            border-radius: var(--radius-md);
            font-size: 14px;
        }

        .search-results {
            padding: var(--spacing-md);
        }

        .result-section {
            margin-bottom: var(--spacing-lg);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .result-count {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .result-item {
            background: white;
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-sm);
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
            cursor: pointer;
            transition: all 0.2s;
        }

        .result-item:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-sm);
        }

        .result-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .result-type {
            background: var(--ios-blue);
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 500;
        }

        .result-content {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.4;
            margin-bottom: var(--spacing-sm);
        }

        .result-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--text-tertiary);
        }

        .highlight {
            background: var(--secondary-color);
            color: white;
            padding: 1px 2px;
            border-radius: 2px;
        }

        .empty-state {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--text-secondary);
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: var(--spacing-md);
            opacity: 0.3;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .empty-subtitle {
            font-size: 14px;
        }

        .search-suggestions {
            background: white;
            margin: var(--spacing-md);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
        }

        .suggestion-item {
            padding: var(--spacing-md);
            border-bottom: 0.5px solid var(--ios-gray5);
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .suggestion-item:hover {
            background: var(--ios-gray6);
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-text {
            font-size: 16px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .suggestion-icon {
            color: var(--ios-gray);
        }

        .clear-filters {
            background: var(--ios-red);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin-top: var(--spacing-md);
        }

        /* 搜索过滤器样式 */
        .search-filters {
            display: flex;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .search-filters::-webkit-scrollbar {
            display: none;
        }

        .filter-chip {
            background: var(--ios-gray6);
            border: 1px solid var(--ios-gray5);
            color: var(--text-primary);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-lg);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .filter-chip:hover {
            background: var(--ios-gray5);
        }

        .filter-chip.active {
            background: var(--ios-blue);
            color: white;
            border-color: var(--ios-blue);
        }

        /* 搜索历史样式 */
        .search-history {
            padding: var(--spacing-md);
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
        }

        .history-title {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-primary);
        }

        .clear-history-btn {
            background: var(--ios-red);
            color: white;
            border: none;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .history-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .history-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
            background: white;
            border-radius: var(--radius-md);
            border: 1px solid var(--ios-gray5);
            cursor: pointer;
            transition: background 0.2s;
        }

        .history-item:hover {
            background: var(--ios-gray6);
        }

        .history-item i {
            color: var(--text-secondary);
        }

        .history-item span {
            flex: 1;
            color: var(--text-primary);
        }

        .remove-history {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            transition: background 0.2s;
        }

        .remove-history:hover {
            background: var(--ios-gray5);
            color: var(--ios-red);
        }

        .empty-history {
            text-align: center;
            color: var(--text-secondary);
            padding: var(--spacing-xl);
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                    </div>
                    <div class="wifi-icon"></div>
                    <span>100%</span>
                    <div class="battery-icon"></div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <button class="nav-button" onclick="Navigator.back()">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">搜索</div>
                <button class="nav-button" onclick="toggleFilters()">
                    筛选
                </button>
            </div>

            <!-- 搜索栏 -->
            <div class="search-header">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="搜索对局记录、技巧、对手..." id="searchInput" autofocus>
                    <button class="clear-button" onclick="clearSearch()" id="clearButton" style="display: none;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- 搜索过滤器 -->
                <div class="search-filters" id="searchFilters">
                    <button class="filter-chip active" data-filter="all" onclick="setSearchFilter('all')">全部</button>
                    <button class="filter-chip" data-filter="gameType" onclick="setSearchFilter('gameType')">游戏类型</button>
                    <button class="filter-chip" data-filter="opponent" onclick="setSearchFilter('opponent')">对手</button>
                    <button class="filter-chip" data-filter="notes" onclick="setSearchFilter('notes')">笔记</button>
                    <button class="filter-chip" data-filter="tags" onclick="setSearchFilter('tags')">标签</button>
                </div>
            </div>

            <!-- 高级筛选 -->
            <div class="filter-section" id="filterSection" style="display: none;">
                <div class="filter-header">
                    <div class="filter-title">
                        <i class="fas fa-filter"></i>
                        高级筛选
                    </div>
                </div>
                <div class="filter-content">
                    <div class="filter-group">
                        <div class="filter-label">内容类型</div>
                        <div class="filter-options" id="typeFilters">
                            <div class="filter-option active" data-type="all">全部</div>
                            <div class="filter-option" data-type="records">对局记录</div>
                            <div class="filter-option" data-type="knowledge">技巧库</div>
                            <div class="filter-option" data-type="opponents">对手信息</div>
                        </div>
                    </div>

                    <div class="filter-group">
                        <div class="filter-label">游戏类型</div>
                        <div class="filter-options" id="gameTypeFilters">
                            <div class="filter-option active" data-game="all">全部</div>
                            <div class="filter-option" data-game="象棋">象棋</div>
                            <div class="filter-option" data-game="围棋">围棋</div>
                            <div class="filter-option" data-game="国际象棋">国际象棋</div>
                            <div class="filter-option" data-game="扑克">扑克</div>
                            <div class="filter-option" data-game="麻将">麻将</div>
                        </div>
                    </div>

                    <div class="filter-group">
                        <div class="filter-label">时间范围</div>
                        <div class="date-range">
                            <input type="date" class="date-input" id="startDate">
                            <span>至</span>
                            <input type="date" class="date-input" id="endDate">
                        </div>
                    </div>

                    <button class="clear-filters" onclick="clearFilters()">
                        清除筛选条件
                    </button>
                </div>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <!-- 搜索建议 -->
                <div class="search-suggestions" id="searchSuggestions">
                    <div class="suggestion-item" onclick="performSearch('象棋开局')">
                        <div class="suggestion-text">
                            <i class="fas fa-search suggestion-icon"></i>
                            象棋开局
                        </div>
                    </div>
                    <div class="suggestion-item" onclick="performSearch('胜率统计')">
                        <div class="suggestion-text">
                            <i class="fas fa-chart-bar suggestion-icon"></i>
                            胜率统计
                        </div>
                    </div>
                    <div class="suggestion-item" onclick="performSearch('最近对局')">
                        <div class="suggestion-text">
                            <i class="fas fa-clock suggestion-icon"></i>
                            最近对局
                        </div>
                    </div>
                    <div class="suggestion-item" onclick="performSearch('技巧总结')">
                        <div class="suggestion-text">
                            <i class="fas fa-lightbulb suggestion-icon"></i>
                            技巧总结
                        </div>
                    </div>
                </div>

                <!-- 搜索历史 -->
                <div class="search-history" id="searchHistory">
                    <div class="history-header">
                        <div class="history-title">
                            <i class="fas fa-history"></i>
                            搜索历史
                        </div>
                        <button class="clear-history-btn" onclick="clearSearchHistory()">
                            <i class="fas fa-trash"></i>
                            清空
                        </button>
                    </div>
                    <div class="history-list" id="historyList">
                        <!-- 动态加载搜索历史 -->
                    </div>
                </div>

                <!-- 搜索结果 -->
                <div class="search-results" id="searchResults" style="display: none;">
                    <!-- 动态加载搜索结果 -->
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="app-utils.js"></script>
    <script>
        let searchQuery = '';
        let searchHistory = [];
        let currentSearchFilter = 'all';
        let filters = {
            type: 'all',
            gameType: 'all',
            startDate: '',
            endDate: ''
        };

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSearchHistory();
            initSearch();
            initFilters();
            showSearchHistory();
        });

        // 初始化搜索
        function initSearch() {
            const searchInput = document.getElementById('searchInput');
            const clearButton = document.getElementById('clearButton');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                const value = this.value.trim();
                clearButton.style.display = value ? 'flex' : 'none';
                
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    searchQuery = value.toLowerCase();
                    if (searchQuery) {
                        performSearch(searchQuery);
                    } else {
                        showSuggestions();
                    }
                }, 300);
            });

            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchQuery = this.value.trim().toLowerCase();
                    if (searchQuery) {
                        performSearch(searchQuery);
                    }
                }
            });
        }

        // 初始化筛选器
        function initFilters() {
            // 内容类型筛选
            document.querySelectorAll('#typeFilters .filter-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('#typeFilters .filter-option').forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                    filters.type = this.dataset.type;
                    if (searchQuery) performSearch(searchQuery);
                });
            });

            // 游戏类型筛选
            document.querySelectorAll('#gameTypeFilters .filter-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('#gameTypeFilters .filter-option').forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                    filters.gameType = this.dataset.game;
                    if (searchQuery) performSearch(searchQuery);
                });
            });

            // 日期筛选
            document.getElementById('startDate').addEventListener('change', function() {
                filters.startDate = this.value;
                if (searchQuery) performSearch(searchQuery);
            });

            document.getElementById('endDate').addEventListener('change', function() {
                filters.endDate = this.value;
                if (searchQuery) performSearch(searchQuery);
            });
        }

        // 执行搜索
        function performSearch(query) {
            if (typeof query === 'string') {
                searchQuery = query.toLowerCase();
                document.getElementById('searchInput').value = query;
                document.getElementById('clearButton').style.display = 'flex';
            }

            const results = {
                records: [],
                knowledge: [],
                opponents: []
            };

            // 使用新的搜索方法
            if (filters.type === 'all' || filters.type === 'records') {
                results.records = appStorage.searchRecords(searchQuery, {
                    gameType: filters.gameType,
                    result: filters.result,
                    dateFrom: filters.dateFrom,
                    dateTo: filters.dateTo
                });
            }

            if (filters.type === 'all' || filters.type === 'knowledge') {
                results.knowledge = appStorage.searchKnowledge(searchQuery, {
                    category: filters.category,
                    gameType: filters.gameType,
                    difficulty: filters.difficulty
                });
            }

            if (filters.type === 'all' || filters.type === 'opponents') {
                const opponents = appStorage.get('opponents') || [];
                results.records = records.filter(record => {
                    const matchesQuery = !searchQuery || 
                        record.opponent.toLowerCase().includes(searchQuery) ||
                        (record.notes && record.notes.toLowerCase().includes(searchQuery)) ||
                        (record.location && record.location.toLowerCase().includes(searchQuery)) ||
                        record.gameType.toLowerCase().includes(searchQuery);
                    
                    const matchesGameType = filters.gameType === 'all' || record.gameType === filters.gameType;
                    
                    const matchesDate = (!filters.startDate || new Date(record.date) >= new Date(filters.startDate)) &&
                                       (!filters.endDate || new Date(record.date) <= new Date(filters.endDate));
                    
                    return matchesQuery && matchesGameType && matchesDate;
                });
            }

            // 搜索技巧库
            if (filters.type === 'all' || filters.type === 'knowledge') {
                const knowledge = appStorage.get('knowledge') || [];
                results.knowledge = knowledge.filter(item => {
                    const matchesQuery = !searchQuery ||
                        item.title.toLowerCase().includes(searchQuery) ||
                        item.content.toLowerCase().includes(searchQuery) ||
                        item.tags.some(tag => tag.toLowerCase().includes(searchQuery));
                    
                    const matchesGameType = filters.gameType === 'all' || item.gameType === filters.gameType;
                    
                    return matchesQuery && matchesGameType;
                });
            }

            // 搜索对手信息
            if (filters.type === 'all' || filters.type === 'opponents') {
                const opponents = appStorage.get('opponents') || [];
                results.opponents = opponents.filter(opponent => {
                    const matchesQuery = !searchQuery ||
                        opponent.name.toLowerCase().includes(searchQuery) ||
                        opponent.style.toLowerCase().includes(searchQuery) ||
                        (opponent.notes && opponent.notes.toLowerCase().includes(searchQuery));
                    
                    return matchesQuery;
                });
            }

            renderSearchResults(results);
        }

        // 渲染搜索结果
        function renderSearchResults(results) {
            const container = document.getElementById('searchResults');
            const suggestions = document.getElementById('searchSuggestions');
            
            suggestions.style.display = 'none';
            container.style.display = 'block';

            const totalResults = results.records.length + results.knowledge.length + results.opponents.length;

            if (totalResults === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="empty-title">未找到相关内容</div>
                        <div class="empty-subtitle">尝试调整搜索关键词或筛选条件</div>
                    </div>
                `;
                return;
            }

            let html = '';

            // 对局记录结果
            if (results.records.length > 0) {
                html += `
                    <div class="result-section">
                        <div class="section-header">
                            <div class="section-title">对局记录</div>
                            <div class="result-count">${results.records.length}条</div>
                        </div>
                        ${results.records.map(record => `
                            <div class="result-item" onclick="Navigator.navigate('record-detail.html', {id: '${record.id}'})">
                                <div class="result-header">
                                    <div>
                                        <div class="result-title">${highlightText(record.gameType + ' vs ' + record.opponent)}</div>
                                    </div>
                                    <div class="result-type">记录</div>
                                </div>
                                <div class="result-content">
                                    ${highlightText(record.notes ? record.notes.substring(0, 100) + '...' : '暂无笔记')}
                                </div>
                                <div class="result-meta">
                                    <span>${DateUtils.formatDate(record.date, 'YYYY-MM-DD')}</span>
                                    <span>结果: ${record.result}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            // 技巧库结果
            if (results.knowledge.length > 0) {
                html += `
                    <div class="result-section">
                        <div class="section-header">
                            <div class="section-title">技巧库</div>
                            <div class="result-count">${results.knowledge.length}条</div>
                        </div>
                        ${results.knowledge.map(item => `
                            <div class="result-item" onclick="Navigator.navigate('knowledge-detail.html', { id: '${item.id}' })"
                                <div class="result-header">
                                    <div>
                                        <div class="result-title">${highlightText(item.title)}</div>
                                    </div>
                                    <div class="result-type">技巧</div>
                                </div>
                                <div class="result-content">
                                    ${highlightText(item.content.substring(0, 100) + '...')}
                                </div>
                                <div class="result-meta">
                                    <span>${item.gameType}</span>
                                    <span>${item.category}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            // 对手信息结果
            if (results.opponents.length > 0) {
                html += `
                    <div class="result-section">
                        <div class="section-header">
                            <div class="section-title">对手信息</div>
                            <div class="result-count">${results.opponents.length}条</div>
                        </div>
                        ${results.opponents.map(opponent => `
                            <div class="result-item" onclick="UIUtils.showToast('功能开发中', 'info')">
                                <div class="result-header">
                                    <div>
                                        <div class="result-title">${highlightText(opponent.name)}</div>
                                    </div>
                                    <div class="result-type">对手</div>
                                </div>
                                <div class="result-content">
                                    ${highlightText(opponent.style)} · 总对局: ${opponent.totalGames}
                                </div>
                                <div class="result-meta">
                                    <span>胜率: ${opponent.totalGames > 0 ? (opponent.wins / opponent.totalGames * 100).toFixed(1) : 0}%</span>
                                    <span>${opponent.level}</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        // 高亮搜索关键词
        function highlightText(text) {
            if (!searchQuery || !text) return text;
            const regex = new RegExp(`(${searchQuery})`, 'gi');
            return text.replace(regex, '<span class="highlight">$1</span>');
        }

        // 显示搜索建议
        function showSuggestions() {
            document.getElementById('searchSuggestions').style.display = 'block';
            document.getElementById('searchResults').style.display = 'none';
        }

        // 设置搜索过滤器
        function setSearchFilter(filter) {
            currentSearchFilter = filter;

            // 更新过滤器按钮状态
            document.querySelectorAll('.filter-chip').forEach(chip => {
                chip.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

            // 如果有搜索内容，重新搜索
            if (searchQuery) {
                performSearch(searchQuery);
            }
        }

        // 加载搜索历史
        function loadSearchHistory() {
            searchHistory = appStorage.get('searchHistory') || [];
        }

        // 保存搜索历史
        function saveSearchHistory(query) {
            if (!query || query.length < 2) return;

            // 移除重复项
            searchHistory = searchHistory.filter(item => item !== query);
            // 添加到开头
            searchHistory.unshift(query);
            // 限制历史记录数量
            searchHistory = searchHistory.slice(0, 10);

            appStorage.set('searchHistory', searchHistory);
        }

        // 显示搜索历史
        function showSearchHistory() {
            const historyContainer = document.getElementById('searchHistory');
            const historyList = document.getElementById('historyList');

            if (searchHistory.length === 0) {
                historyList.innerHTML = '<div class="empty-history">暂无搜索历史</div>';
            } else {
                historyList.innerHTML = searchHistory.map(query =>
                    `<div class="history-item" onclick="searchFromHistory('${query}')">
                        <i class="fas fa-history"></i>
                        <span>${query}</span>
                        <button class="remove-history" onclick="removeFromHistory('${query}', event)">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>`
                ).join('');
            }

            historyContainer.style.display = 'block';
        }

        // 从历史记录搜索
        function searchFromHistory(query) {
            const searchInput = document.getElementById('searchInput');
            searchInput.value = query;
            searchQuery = query;
            performSearch(query);
        }

        // 从历史记录中移除
        function removeFromHistory(query, event) {
            event.stopPropagation();
            searchHistory = searchHistory.filter(item => item !== query);
            appStorage.set('searchHistory', searchHistory);
            showSearchHistory();
        }

        // 清空搜索历史
        function clearSearchHistory() {
            UIUtils.showConfirm(
                '清空搜索历史',
                '确定要清空所有搜索历史吗？',
                () => {
                    searchHistory = [];
                    appStorage.set('searchHistory', searchHistory);
                    showSearchHistory();
                    UIUtils.showToast('搜索历史已清空', 'success');
                }
            );
        }

        // 清除搜索
        function clearSearch() {
            document.getElementById('searchInput').value = '';
            document.getElementById('clearButton').style.display = 'none';
            searchQuery = '';

            // 隐藏搜索结果，显示搜索历史
            document.getElementById('searchResults').style.display = 'none';
            showSearchHistory();
        }

        // 切换筛选器显示
        function toggleFilters() {
            const filterSection = document.getElementById('filterSection');
            filterSection.style.display = filterSection.style.display === 'none' ? 'block' : 'none';
        }

        // 清除筛选条件
        function clearFilters() {
            filters = {
                type: 'all',
                gameType: 'all',
                startDate: '',
                endDate: ''
            };

            // 重置UI
            document.querySelectorAll('.filter-option').forEach(opt => opt.classList.remove('active'));
            document.querySelector('[data-type="all"]').classList.add('active');
            document.querySelector('[data-game="all"]').classList.add('active');
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';

            if (searchQuery) performSearch(searchQuery);
        }
    </script>
</body>
</html>
