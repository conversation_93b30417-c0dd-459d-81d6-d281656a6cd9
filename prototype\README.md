# 棋牌记事本 App 高保真原型

## 📱 项目概述

这是一个专为棋牌爱好者设计的记事本应用的高保真HTML原型，完全模拟iPhone 15 Pro的界面设计，符合iOS人机界面指南。

## 🎯 设计特点

### 📐 设备适配
- **设备尺寸**: iPhone 15 Pro (393×852px)
- **安全区域**: 完整的状态栏、导航栏、标签栏和Home Indicator
- **响应式设计**: 支持不同屏幕尺寸自适应

### 🎨 视觉设计
- **设计语言**: 严格遵循iOS设计规范
- **色彩方案**: 
  - 主色调: 深蓝色 (#1E3A8A) - 专业稳重
  - 辅助色: 金色 (#F59E0B) - 成就感
  - 背景色: 浅灰色 (#F8FAFC) - 护眼舒适
- **字体系统**: iOS系统字体层级
- **图标库**: FontAwesome 6.4.0

### ⚡ 交互体验
- **流畅动画**: CSS3过渡动画
- **触摸优化**: 适合单手操作的按钮尺寸
- **反馈机制**: 点击、悬停状态反馈
- **手势支持**: 模拟iOS原生手势

## 📂 文件结构

```
prototype/
├── index.html          # 主页面 - 首页
├── records.html        # 对局记录页面
├── stats.html          # 统计分析页面
├── knowledge.html      # 技巧知识页面
├── profile.html        # 个人中心页面
├── add-record.html     # 新建记录页面
├── css/
│   └── ios-framework.css   # iOS风格框架样式
├── js/
│   └── app.js          # 应用逻辑和数据管理
└── README.md           # 项目说明文档
```

## 🔧 功能模块

### 🏠 首页 (index.html)
- **欢迎区域**: 个性化问候和激励
- **统计概览**: 总对局数、胜率、对手数
- **快捷操作**: 新建记录、快速记录、查看记录、添加技巧
- **最近记录**: 显示最新的3条对局记录

### 📝 对局记录 (records.html)
- **搜索功能**: 全文搜索对局记录
- **分类筛选**: 按游戏类型、结果筛选
- **记录列表**: 详细的对局信息展示
- **浮动按钮**: 快速添加新记录

### 📊 统计分析 (stats.html)
- **总体统计**: 胜负数据和胜率
- **游戏类型分析**: 各类游戏的表现
- **趋势图表**: 胜率变化趋势
- **对手分析**: 常见对手的对战记录
- **成就系统**: 解锁的成就徽章

### 📚 技巧知识 (knowledge.html)
- **分类浏览**: 开局、中局、残局、技巧、心得
- **搜索功能**: 快速查找技巧内容
- **收藏系统**: 标记重要技巧
- **难度标识**: 初级、中级、高级分类

### 👤 个人中心 (profile.html)
- **个人信息**: 头像、昵称、等级展示
- **数据管理**: iCloud同步、导入导出
- **应用设置**: 通知、隐私、外观设置
- **帮助支持**: 使用帮助、意见反馈

### ➕ 新建记录 (add-record.html)
- **游戏信息**: 类型选择、对手、结果
- **时间地点**: 对局时间、用时、地点
- **详细信息**: 重要程度、标签、笔记
- **图片附件**: 支持添加棋盘截图

## 🚀 使用方法

### 本地预览
1. 下载所有文件到本地目录
2. 使用现代浏览器打开 `index.html`
3. 建议使用Chrome/Safari的设备模拟器查看移动端效果

### 设备模拟
1. 打开浏览器开发者工具 (F12)
2. 切换到设备模拟模式
3. 选择iPhone 15 Pro或自定义393×852分辨率
4. 刷新页面获得最佳体验

### 功能测试
- **页面导航**: 点击底部标签栏切换页面
- **数据交互**: 所有数据存储在localStorage中
- **表单操作**: 新建记录页面支持完整的表单交互
- **搜索筛选**: 记录和技巧页面支持搜索和筛选

## 💡 技术实现

### 前端技术栈
- **HTML5**: 语义化标签和现代HTML特性
- **CSS3**: Flexbox、Grid、动画、变量
- **JavaScript ES6+**: 模块化、类、箭头函数
- **本地存储**: localStorage数据持久化

### 设计系统
- **CSS变量**: 统一的色彩和尺寸管理
- **组件化**: 可复用的UI组件
- **响应式**: 移动优先的设计理念
- **无障碍**: 支持键盘导航和屏幕阅读器

### 性能优化
- **懒加载**: 图片和内容按需加载
- **缓存策略**: 合理的资源缓存
- **动画优化**: 使用transform和opacity
- **代码分割**: 页面级别的代码组织

## 🎨 设计规范

### 色彩规范
```css
/* 品牌色彩 */
--primary-color: #1E3A8A;      /* 主色调 */
--secondary-color: #F59E0B;    /* 辅助色 */
--background-color: #F8FAFC;   /* 背景色 */
--surface-color: #FFFFFF;      /* 表面色 */

/* iOS系统色彩 */
--ios-blue: #007AFF;
--ios-green: #34C759;
--ios-red: #FF3B30;
--ios-orange: #FF9500;
```

### 字体规范
```css
/* iOS字体层级 */
--font-size-large: 34px;       /* 大标题 */
--font-size-title1: 28px;      /* 一级标题 */
--font-size-title2: 22px;      /* 二级标题 */
--font-size-headline: 17px;    /* 标题文字 */
--font-size-body: 17px;        /* 正文 */
--font-size-subhead: 15px;     /* 副标题 */
```

### 间距规范
```css
/* 圆角 */
--border-radius-small: 8px;
--border-radius-medium: 12px;
--border-radius-large: 16px;

/* 阴影 */
--shadow-small: 0 1px 3px rgba(0, 0, 0, 0.1);
--shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
```

## 📱 兼容性

### 浏览器支持
- ✅ Chrome 80+
- ✅ Safari 13+
- ✅ Firefox 75+
- ✅ Edge 80+

### 设备支持
- ✅ iPhone (iOS 14+)
- ✅ iPad (iPadOS 14+)
- ✅ Android (Chrome 80+)
- ✅ 桌面浏览器

## 🔮 后续开发

### 技术迁移
1. **React Native**: 跨平台移动应用开发
2. **SwiftUI**: 原生iOS应用开发
3. **Flutter**: 跨平台解决方案

### 功能扩展
1. **数据同步**: iCloud/Google Drive同步
2. **社交功能**: 好友对战、排行榜
3. **AI分析**: 智能对局分析和建议
4. **离线支持**: PWA渐进式Web应用

## 📄 许可证

本项目仅用于演示和学习目的，请勿用于商业用途。

---

**开发团队**: 棋牌记事本开发组  
**版本**: v1.0.0  
**更新时间**: 2024年1月
