<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>对局记录 - 棋牌记事本</title>
    <link rel="stylesheet" href="css/ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 记录页面特定样式 */
        .filter-bar {
            background: var(--surface-color);
            padding: 12px 16px;
            border-bottom: 0.5px solid var(--border-color);
            display: flex;
            gap: 8px;
            overflow-x: auto;
        }
        
        .filter-chip {
            background: var(--ios-gray6);
            color: var(--text-secondary);
            border: none;
            border-radius: 16px;
            padding: 6px 12px;
            font-size: var(--font-size-caption1);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--animation-fast);
            white-space: nowrap;
            flex-shrink: 0;
        }
        
        .filter-chip.active {
            background: var(--primary-color);
            color: white;
        }
        
        .filter-chip:hover {
            background: var(--ios-gray5);
        }
        
        .filter-chip.active:hover {
            background: #1E40AF;
        }
        
        .records-container {
            padding: 16px;
        }
        
        .record-item {
            background: var(--surface-color);
            border-radius: var(--border-radius-medium);
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: var(--shadow-small);
            cursor: pointer;
            transition: all var(--animation-fast);
        }
        
        .record-item:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }
        
        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }
        
        .record-title {
            font-size: var(--font-size-body);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .record-opponent {
            font-size: var(--font-size-subhead);
            color: var(--text-secondary);
        }
        
        .record-result {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-caption1);
            font-weight: 600;
            text-align: center;
            min-width: 32px;
        }
        
        .result-win {
            background: #DCFCE7;
            color: #166534;
        }
        
        .result-loss {
            background: #FEE2E2;
            color: #991B1B;
        }
        
        .result-draw {
            background: #FEF3C7;
            color: #92400E;
        }
        
        .record-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid var(--ios-gray5);
        }
        
        .record-date {
            font-size: var(--font-size-caption1);
            color: var(--text-tertiary);
        }
        
        .record-duration {
            font-size: var(--font-size-caption1);
            color: var(--text-tertiary);
        }
        
        .record-tags {
            display: flex;
            gap: 4px;
            margin-top: 8px;
        }
        
        .record-tag {
            background: var(--ios-gray6);
            color: var(--text-secondary);
            padding: 2px 6px;
            border-radius: 8px;
            font-size: var(--font-size-caption2);
            font-weight: 500;
        }
        
        .search-bar {
            background: var(--surface-color);
            padding: 12px 16px;
            border-bottom: 0.5px solid var(--border-color);
        }
        
        .search-input {
            width: 100%;
            background: var(--ios-gray6);
            border: none;
            border-radius: 10px;
            padding: 8px 12px 8px 36px;
            font-size: var(--font-size-body);
            color: var(--text-primary);
            position: relative;
        }
        
        .search-input::placeholder {
            color: var(--text-tertiary);
        }
        
        .search-input:focus {
            outline: none;
            background: var(--surface-color);
            box-shadow: 0 0 0 2px var(--primary-color);
        }
        
        .search-icon {
            position: absolute;
            left: 28px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-tertiary);
            font-size: 14px;
            pointer-events: none;
        }
        
        .search-container {
            position: relative;
        }
        
        .floating-add-btn {
            position: fixed;
            bottom: calc(var(--tab-bar-height) + 16px);
            right: 16px;
            width: 56px;
            height: 56px;
            background: var(--primary-color);
            border: none;
            border-radius: 28px;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
            transition: all var(--animation-fast);
            z-index: 100;
        }
        
        .floating-add-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(30, 58, 138, 0.4);
        }
        
        .empty-records {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }
        
        .empty-records-icon {
            font-size: 64px;
            color: var(--ios-gray3);
            margin-bottom: 20px;
        }
        
        .empty-records-title {
            font-size: var(--font-size-title3);
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }
        
        .empty-records-text {
            font-size: var(--font-size-body);
            margin-bottom: 24px;
            line-height: 1.5;
        }
        
        .empty-records-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius-medium);
            padding: 12px 24px;
            font-size: var(--font-size-body);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--animation-fast);
        }
        
        .empty-records-btn:hover {
            background: #1E40AF;
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="device-screen">
            <!-- 记录页面 -->
            <div id="records" class="page">
                <!-- 状态栏 -->
                <div class="status-bar">
                    <div class="status-left">
                        <span class="status-time">14:30</span>
                    </div>
                    <div class="status-center">
                        <i class="fas fa-signal" style="font-size: 10px;"></i>
                        <i class="fas fa-wifi" style="font-size: 10px; margin-left: 4px;"></i>
                    </div>
                    <div class="status-right">
                        <i class="fas fa-battery-three-quarters" style="font-size: 12px;"></i>
                        <span style="font-size: 11px; margin-left: 2px;">85%</span>
                    </div>
                </div>

                <!-- 导航栏 -->
                <div class="nav-bar">
                    <button class="nav-button" data-action="back">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="nav-title">对局记录</div>
                    <button class="nav-button primary" data-action="add-record">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>

                <!-- 搜索栏 -->
                <div class="search-bar">
                    <div class="search-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="搜索对局记录..." id="search-input">
                    </div>
                </div>

                <!-- 筛选栏 -->
                <div class="filter-bar">
                    <button class="filter-chip active" data-filter="all">全部</button>
                    <button class="filter-chip" data-filter="象棋">象棋</button>
                    <button class="filter-chip" data-filter="围棋">围棋</button>
                    <button class="filter-chip" data-filter="国际象棋">国际象棋</button>
                    <button class="filter-chip" data-filter="扑克">扑克</button>
                    <button class="filter-chip" data-filter="麻将">麻将</button>
                    <button class="filter-chip" data-filter="win">胜局</button>
                    <button class="filter-chip" data-filter="loss">负局</button>
                </div>

                <!-- 主内容 -->
                <div class="main-content">
                    <div class="records-container" id="records-list">
                        <!-- 示例记录 -->
                        <div class="record-item" data-action="view-record" data-id="1">
                            <div class="record-header">
                                <div>
                                    <div class="record-title">象棋对局</div>
                                    <div class="record-opponent">vs 张三</div>
                                </div>
                                <div class="record-result result-win">胜</div>
                            </div>
                            <div class="record-tags">
                                <span class="record-tag">重要对局</span>
                                <span class="record-tag">线下对局</span>
                            </div>
                            <div class="record-meta">
                                <div class="record-date">1月15日 14:30</div>
                                <div class="record-duration">45分钟</div>
                            </div>
                        </div>

                        <div class="record-item" data-action="view-record" data-id="2">
                            <div class="record-header">
                                <div>
                                    <div class="record-title">围棋对局</div>
                                    <div class="record-opponent">vs 李四</div>
                                </div>
                                <div class="record-result result-loss">负</div>
                            </div>
                            <div class="record-tags">
                                <span class="record-tag">练习赛</span>
                                <span class="record-tag">在线对局</span>
                            </div>
                            <div class="record-meta">
                                <div class="record-date">1月14日 19:20</div>
                                <div class="record-duration">1小时20分钟</div>
                            </div>
                        </div>

                        <div class="record-item" data-action="view-record" data-id="3">
                            <div class="record-header">
                                <div>
                                    <div class="record-title">国际象棋对局</div>
                                    <div class="record-opponent">vs 王五</div>
                                </div>
                                <div class="record-result result-draw">和</div>
                            </div>
                            <div class="record-tags">
                                <span class="record-tag">比赛</span>
                            </div>
                            <div class="record-meta">
                                <div class="record-date">1月13日 16:45</div>
                                <div class="record-duration">55分钟</div>
                            </div>
                        </div>

                        <!-- 空状态（当没有记录时显示） -->
                        <div class="empty-records" id="empty-records" style="display: none;">
                            <div class="empty-records-icon">
                                <i class="fas fa-chess-board"></i>
                            </div>
                            <div class="empty-records-title">暂无对局记录</div>
                            <div class="empty-records-text">
                                开始记录您的第一局对局<br>
                                追踪进步，提升棋艺
                            </div>
                            <button class="empty-records-btn" data-action="add-record">
                                <i class="fas fa-plus"></i>
                                新建记录
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 浮动添加按钮 -->
                <button class="floating-add-btn" data-action="add-record">
                    <i class="fas fa-plus"></i>
                </button>

                <!-- 标签栏 -->
                <div class="tab-bar">
                    <div class="tab-item" data-page="home">
                        <div class="tab-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="tab-label">首页</div>
                    </div>
                    <div class="tab-item active" data-page="records">
                        <div class="tab-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="tab-label">记录</div>
                    </div>
                    <div class="tab-item" data-page="stats">
                        <div class="tab-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="tab-label">统计</div>
                    </div>
                    <div class="tab-item" data-page="knowledge">
                        <div class="tab-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="tab-label">技巧</div>
                    </div>
                    <div class="tab-item" data-page="profile">
                        <div class="tab-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="tab-label">我的</div>
                    </div>
                </div>

                <!-- Home Indicator -->
                <div class="home-indicator"></div>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script>
        // 记录页面特定功能
        document.addEventListener('DOMContentLoaded', function() {
            // 筛选功能
            document.querySelectorAll('.filter-chip').forEach(chip => {
                chip.addEventListener('click', function() {
                    // 移除所有active状态
                    document.querySelectorAll('.filter-chip').forEach(c => c.classList.remove('active'));
                    // 添加当前active状态
                    this.classList.add('active');
                    
                    // 这里可以添加筛选逻辑
                    const filter = this.dataset.filter;
                    console.log('筛选:', filter);
                });
            });

            // 搜索功能
            const searchInput = document.getElementById('search-input');
            searchInput.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                console.log('搜索:', query);
                // 这里可以添加搜索逻辑
            });
        });
    </script>
</body>
</html>
