<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>棋牌记事本</title>
    <link rel="stylesheet" href="ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .welcome-section {
            padding: var(--spacing-lg) var(--spacing-md);
            text-align: center;
        }

        .welcome-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-sm);
        }

        .welcome-subtitle {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xl);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin: var(--spacing-md);
        }

        .stat-card {
            background: white;
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
            box-shadow: var(--shadow-md);
            border: 0.5px solid var(--ios-gray5);
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .quick-actions {
            margin: var(--spacing-md);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            padding: 0 var(--spacing-sm);
        }

        .action-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }

        .action-card {
            background: white;
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            color: inherit;
        }

        .action-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .action-icon {
            width: 48px;
            height: 48px;
            background: var(--ios-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-md);
            font-size: 24px;
            color: white;
        }

        .action-card:nth-child(2) .action-icon { background: var(--ios-green); }
        .action-card:nth-child(3) .action-icon { background: var(--ios-orange); }
        .action-card:nth-child(4) .action-icon { background: var(--ios-purple); }

        .action-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .action-subtitle {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .recent-games {
            margin: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
        }

        .game-item {
            background: white;
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-sm);
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .game-item:hover {
            background-color: var(--ios-gray6);
        }

        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .game-type {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .game-result {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            color: white;
        }

        .game-result.win { background: var(--ios-green); }
        .game-result.lose { background: var(--ios-red); }
        .game-result.draw { background: var(--ios-orange); }

        .game-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .floating-add {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: var(--ios-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-lg);
            cursor: pointer;
            transition: all 0.2s;
            z-index: 50;
        }

        .floating-add:hover {
            transform: scale(1.1);
        }

        .floating-add i {
            color: white;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                    </div>
                    <div class="wifi-icon"></div>
                    <span>100%</span>
                    <div class="battery-icon"></div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <div class="nav-title">棋牌记事本</div>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <!-- 欢迎区域 -->
                <div class="welcome-section">
                    <div class="welcome-title">欢迎回来</div>
                    <div class="welcome-subtitle">记录每一局精彩对弈</div>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card" onclick="Navigator.navigate('records.html')">
                        <div class="stat-number" id="totalGames">0</div>
                        <div class="stat-label">总对局</div>
                        <div class="stat-hint">点击查看详情</div>
                    </div>
                    <div class="stat-card" onclick="Navigator.navigate('statistics.html')">
                        <div class="stat-number" id="winRate">0%</div>
                        <div class="stat-label">胜率</div>
                        <div class="stat-hint">点击查看分析</div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="quick-actions">
                    <div class="section-title">快捷操作</div>
                    <div class="action-grid">
                        <a href="add-record.html" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="action-title">新增记录</div>
                            <div class="action-subtitle">记录新对局</div>
                        </a>
                        <a href="records.html" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-list"></i>
                            </div>
                            <div class="action-title">对局记录</div>
                            <div class="action-subtitle">查看历史</div>
                        </a>
                        <a href="statistics.html" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="action-title">数据统计</div>
                            <div class="action-subtitle">分析表现</div>
                        </a>
                        <a href="knowledge.html" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="action-title">棋艺技巧</div>
                            <div class="action-subtitle">学习提升</div>
                        </a>
                    </div>
                </div>

                <!-- 最近对局 -->
                <div class="recent-games">
                    <div class="section-title">最近对局</div>
                    <div id="recentGamesList">
                        <!-- 动态加载最近对局 -->
                    </div>
                </div>
            </div>

            <!-- 底部标签栏 -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item active" data-tab="home">
                    <div class="tab-icon"><i class="fas fa-home"></i></div>
                    <div class="tab-label">首页</div>
                </a>
                <a href="records.html" class="tab-item" data-tab="records">
                    <div class="tab-icon"><i class="fas fa-list"></i></div>
                    <div class="tab-label">记录</div>
                </a>
                <a href="statistics.html" class="tab-item" data-tab="statistics">
                    <div class="tab-icon"><i class="fas fa-chart-bar"></i></div>
                    <div class="tab-label">统计</div>
                </a>
                <a href="knowledge.html" class="tab-item" data-tab="knowledge">
                    <div class="tab-icon"><i class="fas fa-book"></i></div>
                    <div class="tab-label">技巧</div>
                </a>
            </div>

            <!-- 悬浮添加按钮 -->
            <div class="floating-add" onclick="Navigator.navigate('add-record.html')">
                <i class="fas fa-plus"></i>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="app-utils.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化示例数据（如果没有数据的话）
            appStorage.initSampleData();

            loadDashboardData();
            loadRecentGames();
            UIUtils.updateStatusBar();
            UIUtils.animatePageEnter();

            // 添加动画效果
            setTimeout(() => {
                const cards = document.querySelectorAll('.stat-card, .quick-action, .recent-item');
                cards.forEach((card, index) => {
                    UIUtils.animateElement(card, 'fade-in', index * 100);
                    card.classList.add('hover-lift');
                });
            }, 300);

            // 为按钮添加涟漪效果
            const buttons = document.querySelectorAll('.button-primary, .quick-action');
            buttons.forEach(button => {
                UIUtils.addRippleEffect(button);
                button.classList.add('button-feedback');
            });

            // 定时更新状态栏
            setInterval(UIUtils.updateStatusBar, 60000);
        });

        // 加载仪表板数据
        function loadDashboardData() {
            const records = appStorage.get('gameRecords') || [];
            const stats = appStorage.get('statistics') || {};

            // 计算总对局数
            const totalGames = records.length;
            document.getElementById('totalGames').textContent = totalGames;

            // 计算胜率
            const totalWins = records.filter(record => record.result === '胜').length;
            const winRate = totalGames > 0 ? (totalWins / totalGames * 100).toFixed(1) : 0;
            document.getElementById('winRate').textContent = winRate + '%';

            // 添加动画效果
            animateNumbers();
        }

        // 数字动画效果
        function animateNumbers() {
            const totalGamesEl = document.getElementById('totalGames');
            const winRateEl = document.getElementById('winRate');

            totalGamesEl.classList.add('animate-scale-in');
            winRateEl.classList.add('animate-scale-in');

            setTimeout(() => {
                totalGamesEl.classList.remove('animate-scale-in');
                winRateEl.classList.remove('animate-scale-in');
            }, 600);
        }



        // 加载最近对局
        function loadRecentGames() {
            const records = appStorage.get('gameRecords') || [];
            const recentRecords = records.slice(0, 3);
            const container = document.getElementById('recentGamesList');
            
            if (recentRecords.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px 20px; color: var(--text-secondary);">
                        <i class="fas fa-chess-board" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                        <div>还没有对局记录</div>
                        <div style="font-size: 14px; margin-top: 8px;">点击上方按钮开始记录</div>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = recentRecords.map(record => `
                <div class="game-item" onclick="Navigator.navigate('record-detail.html', {id: '${record.id}'})">
                    <div class="game-header">
                        <div class="game-type">${record.gameType} vs ${record.opponent}</div>
                        <div class="game-result ${record.result === '胜' ? 'win' : record.result === '负' ? 'lose' : 'draw'}">
                            ${record.result}
                        </div>
                    </div>
                    <div class="game-details">
                        <span>${DateUtils.getRelativeTime(record.date)}</span>
                        <span>${record.duration || '未记录'}</span>
                    </div>
                </div>
            `).join('');
        }
    </script>
</body>
</html>
