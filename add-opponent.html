<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加对手</title>
    <link rel="stylesheet" href="ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .form-section {
            background: white;
            margin: var(--spacing-md);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-sm);
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            border: 1px solid var(--ios-gray4);
            border-radius: var(--radius-md);
            font-size: 16px;
            background: white;
            transition: all 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .form-textarea {
            min-height: 80px;
            resize: vertical;
            font-family: inherit;
        }

        .avatar-selector {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .avatar-preview {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--ios-blue), var(--ios-purple));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .avatar-preview:hover {
            transform: scale(1.05);
        }

        .avatar-input {
            flex: 1;
        }

        .level-selector {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-sm);
        }

        .level-option {
            padding: var(--spacing-md);
            border: 1px solid var(--ios-gray4);
            border-radius: var(--radius-md);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }

        .level-option.selected {
            background: var(--ios-blue);
            color: white;
            border-color: var(--ios-blue);
        }

        .level-option:hover {
            border-color: var(--ios-blue);
        }

        .level-icon {
            font-size: 20px;
            margin-bottom: var(--spacing-xs);
        }

        .level-text {
            font-size: 12px;
            font-weight: 500;
        }

        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
        }

        .action-button {
            flex: 1;
            padding: var(--spacing-md);
            border-radius: var(--radius-lg);
            font-size: 16px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-button.primary {
            background: var(--ios-blue);
            color: white;
        }

        .action-button.secondary {
            background: var(--ios-gray6);
            color: var(--text-primary);
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .action-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .char-counter {
            font-size: 12px;
            color: var(--text-secondary);
            text-align: right;
            margin-top: var(--spacing-xs);
        }

        .char-counter.warning {
            color: var(--ios-orange);
        }

        .char-counter.error {
            color: var(--ios-red);
        }

        .form-hint {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: var(--spacing-xs);
            line-height: 1.4;
        }

        .draft-indicator {
            position: fixed;
            top: 100px;
            right: 20px;
            background: var(--ios-orange);
            color: white;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s;
            z-index: 100;
        }

        .draft-indicator.show {
            opacity: 1;
            transform: translateY(0);
        }

        .validation-error {
            color: var(--ios-red);
            font-size: 12px;
            margin-top: var(--spacing-xs);
            display: none;
        }

        .form-input.error {
            border-color: var(--ios-red);
            box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1);
        }

        .quick-fill {
            display: flex;
            gap: var(--spacing-xs);
            margin-top: var(--spacing-sm);
            flex-wrap: wrap;
        }

        .quick-fill-tag {
            background: var(--ios-gray6);
            color: var(--text-secondary);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .quick-fill-tag:hover {
            background: var(--ios-blue);
            color: white;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                    </div>
                    <div class="wifi-icon"></div>
                    <span>100%</span>
                    <div class="battery-icon"></div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <button class="nav-button" onclick="Navigator.back()">
                    <i class="fas fa-chevron-left"></i> 取消
                </button>
                <div class="nav-title" id="pageTitle">添加对手</div>
                <button class="nav-button" onclick="saveOpponent()">
                    <i class="fas fa-check"></i> 保存
                </button>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-user"></i>
                        基本信息
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">头像与姓名</label>
                        <div class="avatar-selector">
                            <div class="avatar-preview" id="avatarPreview" onclick="updateAvatar()">对</div>
                            <div class="avatar-input">
                                <input type="text" class="form-input" id="opponentName" placeholder="输入对手姓名" maxlength="20">
                                <div class="char-counter" id="nameCounter">0/20</div>
                            </div>
                        </div>
                        <div class="validation-error" id="nameError">请输入对手姓名</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">水平等级</label>
                        <div class="level-selector">
                            <div class="level-option" data-level="初级">
                                <div class="level-icon">🌱</div>
                                <div class="level-text">初级</div>
                            </div>
                            <div class="level-option" data-level="中级">
                                <div class="level-icon">🌿</div>
                                <div class="level-text">中级</div>
                            </div>
                            <div class="level-option" data-level="高级">
                                <div class="level-icon">🌳</div>
                                <div class="level-text">高级</div>
                            </div>
                            <div class="level-option" data-level="专业">
                                <div class="level-icon">🏆</div>
                                <div class="level-text">专业</div>
                            </div>
                            <div class="level-option" data-level="大师">
                                <div class="level-icon">👑</div>
                                <div class="level-text">大师</div>
                            </div>
                            <div class="level-option" data-level="未知">
                                <div class="level-icon">❓</div>
                                <div class="level-text">未知</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 棋风特点 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-chess"></i>
                        棋风特点
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">棋风描述</label>
                        <input type="text" class="form-input" id="opponentStyle" placeholder="如：稳健型、攻击型、防守型等" maxlength="50">
                        <div class="char-counter" id="styleCounter">0/50</div>
                        <div class="quick-fill">
                            <span class="quick-fill-tag" onclick="fillStyle('稳健型')">稳健型</span>
                            <span class="quick-fill-tag" onclick="fillStyle('攻击型')">攻击型</span>
                            <span class="quick-fill-tag" onclick="fillStyle('防守型')">防守型</span>
                            <span class="quick-fill-tag" onclick="fillStyle('技巧型')">技巧型</span>
                            <span class="quick-fill-tag" onclick="fillStyle('心理型')">心理型</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">优势特点</label>
                        <textarea class="form-input form-textarea" id="strengths" placeholder="描述对手的优势和强项..." maxlength="200"></textarea>
                        <div class="char-counter" id="strengthsCounter">0/200</div>
                        <div class="form-hint">记录对手的强项有助于制定针对性策略</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">劣势特点</label>
                        <textarea class="form-input form-textarea" id="weaknesses" placeholder="描述对手的弱点和不足..." maxlength="200"></textarea>
                        <div class="char-counter" id="weaknessesCounter">0/200</div>
                        <div class="form-hint">了解对手弱点有助于寻找突破口</div>
                    </div>
                </div>

                <!-- 备注信息 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-sticky-note"></i>
                        备注信息
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">其他备注</label>
                        <textarea class="form-input form-textarea" id="notes" placeholder="记录其他重要信息..." maxlength="300"></textarea>
                        <div class="char-counter" id="notesCounter">0/300</div>
                        <div class="form-hint">可以记录对手的习惯、偏好、历史表现等</div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="action-button secondary" onclick="resetForm()">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                    <button class="action-button primary" onclick="saveOpponent()">
                        <i class="fas fa-save"></i> 保存对手
                    </button>
                </div>
            </div>

            <!-- 草稿指示器 -->
            <div class="draft-indicator" id="draftIndicator">
                <i class="fas fa-save"></i> 草稿已保存
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="app-utils.js"></script>
    <script>
        let isEditMode = false;
        let currentOpponentId = null;
        let selectedLevel = '未知';
        let draftTimer = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            const params = Navigator.getParams();
            if (params.id) {
                isEditMode = true;
                currentOpponentId = params.id;
                document.getElementById('pageTitle').textContent = '编辑对手';
                loadOpponentData(params.id);
            } else {
                loadDraft();
            }

            initFormValidation();
            initCharCounters();
            initLevelSelector();
            initAutoSave();
        });

        // 加载对手数据（编辑模式）
        function loadOpponentData(opponentId) {
            const opponents = appStorage.get('opponents') || [];
            const opponent = opponents.find(o => o.id === opponentId);

            if (!opponent) {
                UIUtils.showToast('对手信息不存在', 'error');
                Navigator.back();
                return;
            }

            document.getElementById('opponentName').value = opponent.name;
            document.getElementById('opponentStyle').value = opponent.style || '';
            document.getElementById('strengths').value = opponent.strengths || '';
            document.getElementById('weaknesses').value = opponent.weaknesses || '';
            document.getElementById('notes').value = opponent.notes || '';

            selectedLevel = opponent.level || '未知';
            updateLevelSelection();
            updateAvatar();
            updateCharCounters();
        }

        // 初始化表单验证
        function initFormValidation() {
            const nameInput = document.getElementById('opponentName');
            nameInput.addEventListener('input', function() {
                validateName();
                updateAvatar();
            });

            nameInput.addEventListener('blur', validateName);
        }

        // 验证姓名
        function validateName() {
            const nameInput = document.getElementById('opponentName');
            const nameError = document.getElementById('nameError');
            const name = nameInput.value.trim();

            if (!name) {
                nameInput.classList.add('error');
                nameError.style.display = 'block';
                nameError.textContent = '请输入对手姓名';
                return false;
            }

            // 检查重复姓名（编辑模式除外）
            if (!isEditMode) {
                const opponents = appStorage.get('opponents') || [];
                if (opponents.some(o => o.name === name)) {
                    nameInput.classList.add('error');
                    nameError.style.display = 'block';
                    nameError.textContent = '该对手已存在';
                    return false;
                }
            }

            nameInput.classList.remove('error');
            nameError.style.display = 'none';
            return true;
        }

        // 初始化字符计数器
        function initCharCounters() {
            const inputs = [
                { id: 'opponentName', counter: 'nameCounter', max: 20 },
                { id: 'opponentStyle', counter: 'styleCounter', max: 50 },
                { id: 'strengths', counter: 'strengthsCounter', max: 200 },
                { id: 'weaknesses', counter: 'weaknessesCounter', max: 200 },
                { id: 'notes', counter: 'notesCounter', max: 300 }
            ];

            inputs.forEach(input => {
                const element = document.getElementById(input.id);
                const counter = document.getElementById(input.counter);

                element.addEventListener('input', function() {
                    updateCharCounter(this, counter, input.max);
                });
            });

            updateCharCounters();
        }

        // 更新字符计数器
        function updateCharCounter(input, counter, max) {
            const length = input.value.length;
            counter.textContent = `${length}/${max}`;

            counter.className = 'char-counter';
            if (length > max * 0.9) {
                counter.classList.add('warning');
            }
            if (length >= max) {
                counter.classList.add('error');
            }
        }

        // 更新所有字符计数器
        function updateCharCounters() {
            const inputs = [
                { id: 'opponentName', counter: 'nameCounter', max: 20 },
                { id: 'opponentStyle', counter: 'styleCounter', max: 50 },
                { id: 'strengths', counter: 'strengthsCounter', max: 200 },
                { id: 'weaknesses', counter: 'weaknessesCounter', max: 200 },
                { id: 'notes', counter: 'notesCounter', max: 300 }
            ];

            inputs.forEach(input => {
                const element = document.getElementById(input.id);
                const counter = document.getElementById(input.counter);
                updateCharCounter(element, counter, input.max);
            });
        }

        // 初始化等级选择器
        function initLevelSelector() {
            const options = document.querySelectorAll('.level-option');
            options.forEach(option => {
                option.addEventListener('click', function() {
                    options.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedLevel = this.dataset.level;
                });
            });

            updateLevelSelection();
        }

        // 更新等级选择
        function updateLevelSelection() {
            const options = document.querySelectorAll('.level-option');
            options.forEach(option => {
                option.classList.remove('selected');
                if (option.dataset.level === selectedLevel) {
                    option.classList.add('selected');
                }
            });
        }

        // 更新头像
        function updateAvatar() {
            const name = document.getElementById('opponentName').value.trim();
            const avatar = document.getElementById('avatarPreview');
            avatar.textContent = name ? name.charAt(0).toUpperCase() : '对';
        }

        // 快速填充棋风
        function fillStyle(style) {
            document.getElementById('opponentStyle').value = style;
            updateCharCounters();
        }

        // 初始化自动保存
        function initAutoSave() {
            const inputs = ['opponentName', 'opponentStyle', 'strengths', 'weaknesses', 'notes'];

            inputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                input.addEventListener('input', function() {
                    clearTimeout(draftTimer);
                    draftTimer = setTimeout(saveDraft, 2000);
                });
            });
        }

        // 保存草稿
        function saveDraft() {
            if (isEditMode) return; // 编辑模式不保存草稿

            const draftData = {
                name: document.getElementById('opponentName').value,
                style: document.getElementById('opponentStyle').value,
                strengths: document.getElementById('strengths').value,
                weaknesses: document.getElementById('weaknesses').value,
                notes: document.getElementById('notes').value,
                level: selectedLevel,
                timestamp: Date.now()
            };

            appStorage.set('opponentDraft', draftData);
            showDraftIndicator();
        }

        // 加载草稿
        function loadDraft() {
            const draft = appStorage.get('opponentDraft');
            if (!draft) return;

            // 检查草稿是否过期（24小时）
            if (Date.now() - draft.timestamp > 24 * 60 * 60 * 1000) {
                appStorage.remove('opponentDraft');
                return;
            }

            // 询问是否恢复草稿
            UIUtils.showDialog(
                '发现草稿',
                '检测到未完成的对手信息，是否恢复？',
                [
                    { text: '忽略', action: () => appStorage.remove('opponentDraft') },
                    { text: '恢复', action: restoreDraft }
                ]
            );
        }

        // 恢复草稿
        function restoreDraft() {
            const draft = appStorage.get('opponentDraft');
            if (!draft) return;

            document.getElementById('opponentName').value = draft.name || '';
            document.getElementById('opponentStyle').value = draft.style || '';
            document.getElementById('strengths').value = draft.strengths || '';
            document.getElementById('weaknesses').value = draft.weaknesses || '';
            document.getElementById('notes').value = draft.notes || '';

            selectedLevel = draft.level || '未知';
            updateLevelSelection();
            updateAvatar();
            updateCharCounters();

            UIUtils.showToast('草稿已恢复', 'success');
        }

        // 显示草稿指示器
        function showDraftIndicator() {
            const indicator = document.getElementById('draftIndicator');
            indicator.classList.add('show');
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 2000);
        }

        // 保存对手
        function saveOpponent() {
            if (!validateForm()) {
                return;
            }

            const opponentData = {
                id: isEditMode ? currentOpponentId : Date.now().toString() + Math.random().toString(36).substr(2, 9),
                name: document.getElementById('opponentName').value.trim(),
                avatar: document.getElementById('opponentName').value.trim().charAt(0).toUpperCase(),
                level: selectedLevel,
                style: document.getElementById('opponentStyle').value.trim() || '待分析',
                strengths: document.getElementById('strengths').value.trim() || '',
                weaknesses: document.getElementById('weaknesses').value.trim() || '',
                notes: document.getElementById('notes').value.trim() || '',
                createdAt: isEditMode ? null : new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            let opponents = appStorage.get('opponents') || [];

            if (isEditMode) {
                // 更新现有对手
                const index = opponents.findIndex(o => o.id === currentOpponentId);
                if (index !== -1) {
                    // 保留统计数据
                    const existingOpponent = opponents[index];
                    opponentData.totalGames = existingOpponent.totalGames || 0;
                    opponentData.wins = existingOpponent.wins || 0;
                    opponentData.losses = existingOpponent.losses || 0;
                    opponentData.draws = existingOpponent.draws || 0;
                    opponentData.recentGames = existingOpponent.recentGames || [];
                    opponentData.createdAt = existingOpponent.createdAt;

                    opponents[index] = opponentData;
                }
            } else {
                // 添加新对手
                opponentData.totalGames = 0;
                opponentData.wins = 0;
                opponentData.losses = 0;
                opponentData.draws = 0;
                opponentData.recentGames = [];

                opponents.push(opponentData);
            }

            appStorage.set('opponents', opponents);

            // 清除草稿
            if (!isEditMode) {
                appStorage.remove('opponentDraft');
            }

            UIUtils.showToast(isEditMode ? '对手信息已更新' : '对手已添加', 'success');
            Navigator.back();
        }

        // 验证表单
        function validateForm() {
            let isValid = true;

            // 验证姓名
            if (!validateName()) {
                isValid = false;
            }

            return isValid;
        }

        // 重置表单
        function resetForm() {
            UIUtils.showDialog(
                '重置表单',
                '确定要重置所有内容吗？未保存的内容将丢失。',
                [
                    { text: '取消', action: () => {} },
                    {
                        text: '重置',
                        action: () => {
                            document.getElementById('opponentName').value = '';
                            document.getElementById('opponentStyle').value = '';
                            document.getElementById('strengths').value = '';
                            document.getElementById('weaknesses').value = '';
                            document.getElementById('notes').value = '';

                            selectedLevel = '未知';
                            updateLevelSelection();
                            updateAvatar();
                            updateCharCounters();

                            // 清除验证错误
                            document.querySelectorAll('.form-input').forEach(input => {
                                input.classList.remove('error');
                            });
                            document.querySelectorAll('.validation-error').forEach(error => {
                                error.style.display = 'none';
                            });

                            UIUtils.showToast('表单已重置', 'info');
                        }
                    }
                ]
            );
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                Navigator.back();
            } else if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                saveOpponent();
            } else if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                resetForm();
            }
        });

        // 页面离开前提醒
        window.addEventListener('beforeunload', function(e) {
            const hasContent = document.getElementById('opponentName').value.trim() ||
                             document.getElementById('opponentStyle').value.trim() ||
                             document.getElementById('strengths').value.trim() ||
                             document.getElementById('weaknesses').value.trim() ||
                             document.getElementById('notes').value.trim();

            if (hasContent && !isEditMode) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    </script>
</body>
</html>
