<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技巧库</title>
    <link rel="stylesheet" href="ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .search-section {
            padding: var(--spacing-md);
            background: var(--background-color);
        }

        .search-container {
            background: white;
            border-radius: var(--radius-lg);
            border: 1px solid var(--ios-gray4);
            display: flex;
            align-items: center;
            padding: var(--spacing-sm) var(--spacing-md);
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            padding: var(--spacing-sm) 0;
            background: transparent;
        }

        .search-icon {
            color: var(--ios-gray);
            margin-right: var(--spacing-sm);
        }

        .category-tabs {
            display: flex;
            padding: 0 var(--spacing-md);
            gap: var(--spacing-sm);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .category-tabs::-webkit-scrollbar {
            display: none;
        }

        .category-tab {
            background: white;
            border: 1px solid var(--ios-gray4);
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
        }

        .category-tab.active {
            background: var(--ios-blue);
            color: white;
            border-color: var(--ios-blue);
        }

        .knowledge-list {
            padding: var(--spacing-md);
        }

        .knowledge-item {
            background: white;
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
            cursor: pointer;
            transition: all 0.2s;
        }

        .knowledge-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .knowledge-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-sm);
        }

        .knowledge-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
            line-height: 1.3;
        }

        .knowledge-category {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            background: var(--ios-gray6);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .knowledge-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .game-type-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .difficulty-stars {
            display: flex;
            gap: 2px;
        }

        .difficulty-star {
            color: var(--secondary-color);
            font-size: 12px;
        }

        .knowledge-content {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.5;
            margin-bottom: var(--spacing-sm);
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .knowledge-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .knowledge-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }

        .knowledge-tag {
            background: var(--ios-blue);
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 500;
        }

        .favorite-button {
            background: none;
            border: none;
            color: var(--ios-gray);
            font-size: 18px;
            cursor: pointer;
            transition: color 0.2s;
        }

        .favorite-button.active {
            color: var(--ios-red);
        }

        .floating-add {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: var(--ios-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-lg);
            cursor: pointer;
            transition: all 0.2s;
            z-index: 50;
        }

        .floating-add:hover {
            transform: scale(1.1);
        }

        .floating-add i {
            color: white;
            font-size: 24px;
        }

        .empty-state {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--text-secondary);
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: var(--spacing-md);
            opacity: 0.3;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .empty-subtitle {
            font-size: 14px;
            margin-bottom: var(--spacing-lg);
        }

        .featured-section {
            margin: var(--spacing-md);
            background: white;
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
        }

        .featured-header {
            padding: var(--spacing-md);
            background: linear-gradient(135deg, var(--primary-color), var(--ios-blue));
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .featured-title {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .custom-add-button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--radius-md);
            color: white;
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
            backdrop-filter: blur(10px);
        }

        .custom-add-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-1px);
        }

        .custom-add-button:active {
            transform: translateY(0);
            background: rgba(255, 255, 255, 0.1);
        }

        .custom-add-button i {
            font-size: 10px;
        }

        /* 移动端优化 */
        @media (max-width: 480px) {
            .custom-add-button {
                padding: 6px 8px;
                font-size: 11px;
            }

            .custom-add-button span {
                display: none;
            }

            .custom-add-button i {
                font-size: 12px;
            }
        }

        .featured-content {
            padding: var(--spacing-md);
        }

        .featured-item {
            display: flex;
            align-items: center;
            padding: var(--spacing-sm) 0;
            border-bottom: 0.5px solid var(--ios-gray5);
            cursor: pointer;
        }

        .featured-item:last-child {
            border-bottom: none;
        }

        .featured-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--ios-gray6);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--spacing-md);
            font-size: 14px;
        }

        .featured-info {
            flex: 1;
        }

        .featured-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .featured-desc {
            font-size: 12px;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                    </div>
                    <div class="wifi-icon"></div>
                    <span>100%</span>
                    <div class="battery-icon"></div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <div class="nav-title">技巧库</div>
                <button class="nav-button" onclick="Navigator.navigate('search.html')">
                    <i class="fas fa-search"></i>
                </button>
            </div>

            <!-- 搜索栏 -->
            <div class="search-section">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="搜索技巧、开局、残局..." id="searchInput">
                </div>
            </div>

            <!-- 分类标签 -->
            <div class="category-tabs" id="categoryTabs">
                <div class="category-tab active" data-category="all">全部</div>
                <div class="category-tab" data-category="开局">开局</div>
                <div class="category-tab" data-category="中局">中局</div>
                <div class="category-tab" data-category="残局">残局</div>
                <div class="category-tab" data-category="技巧">技巧</div>
                <div class="category-tab" data-category="战术">战术</div>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <!-- 精选推荐 -->
                <div class="featured-section" id="featuredSection">
                    <div class="featured-header">
                        <div class="featured-title">
                            <i class="fas fa-star"></i>
                            精选推荐
                        </div>
                        <button class="custom-add-button" onclick="addCustomKnowledge()" title="新增自定义技巧">
                            <i class="fas fa-plus"></i>
                            <span>自定义</span>
                        </button>
                    </div>
                    <div class="featured-content">
                        <div class="featured-item" onclick="openFeaturedKnowledge('象棋')">
                            <div class="featured-icon">♟️</div>
                            <div class="featured-info">
                                <div class="featured-name">象棋基本开局原则</div>
                                <div class="featured-desc">掌握开局的基本要领</div>
                            </div>
                        </div>
                        <div class="featured-item" onclick="openFeaturedKnowledge('围棋')">
                            <div class="featured-icon">⚫</div>
                            <div class="featured-info">
                                <div class="featured-name">围棋定式入门</div>
                                <div class="featured-desc">常见定式的应用</div>
                            </div>
                        </div>
                        <div class="featured-item" onclick="openFeaturedKnowledge('扑克')">
                            <div class="featured-icon">🃏</div>
                            <div class="featured-info">
                                <div class="featured-name">德州扑克概率计算</div>
                                <div class="featured-desc">提高胜率的数学基础</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 技巧列表 -->
                <div class="knowledge-list" id="knowledgeList">
                    <!-- 动态加载技巧列表 -->
                </div>
            </div>

            <!-- 底部标签栏 -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item" data-tab="home">
                    <div class="tab-icon"><i class="fas fa-home"></i></div>
                    <div class="tab-label">首页</div>
                </a>
                <a href="records.html" class="tab-item" data-tab="records">
                    <div class="tab-icon"><i class="fas fa-list"></i></div>
                    <div class="tab-label">记录</div>
                </a>
                <a href="statistics.html" class="tab-item" data-tab="statistics">
                    <div class="tab-icon"><i class="fas fa-chart-bar"></i></div>
                    <div class="tab-label">统计</div>
                </a>
                <a href="knowledge.html" class="tab-item active" data-tab="knowledge">
                    <div class="tab-icon"><i class="fas fa-book"></i></div>
                    <div class="tab-label">技巧</div>
                </a>
            </div>

            <!-- 悬浮添加按钮 -->
            <div class="floating-add" onclick="Navigator.navigate('add-knowledge.html')">
                <i class="fas fa-plus"></i>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="app-utils.js"></script>
    <script>
        let currentCategory = 'all';
        let searchQuery = '';
        let showFavoritesOnly = false;

        // 示例技巧数据
        const sampleKnowledge = [
            {
                id: '1',
                title: '象棋开局基本原则',
                category: '开局',
                gameType: '象棋',
                content: '象棋开局的基本原则包括：1. 快速出子，占据中心；2. 保护主帅安全；3. 协调各子配合；4. 避免重复走子。掌握这些原则能够帮助你在开局阶段取得优势。',
                difficulty: 2,
                tags: ['基础', '开局', '原则'],
                isFavorite: false,
                createdAt: new Date().toISOString()
            },
            {
                id: '2',
                title: '围棋基本定式',
                category: '开局',
                gameType: '围棋',
                content: '围棋定式是经过长期实战检验的固定下法。常见的定式包括星位定式、小目定式等。学习定式有助于提高布局效率和避免明显错误。',
                difficulty: 3,
                tags: ['定式', '布局', '基础'],
                isFavorite: true,
                createdAt: new Date().toISOString()
            },
            {
                id: '3',
                title: '德州扑克起手牌选择',
                category: '技巧',
                gameType: '扑克',
                content: '在德州扑克中，起手牌的选择至关重要。优质起手牌包括大对子(AA, KK, QQ)、同花大牌(AK, AQ)等。根据位置和对手情况调整起手牌范围。',
                difficulty: 4,
                tags: ['起手牌', '策略', '概率'],
                isFavorite: false,
                createdAt: new Date().toISOString()
            }
        ];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSampleData();
            initCategoryTabs();
            initSearch();
            loadKnowledge();
        });

        // 初始化示例数据
        function initSampleData() {
            const existingKnowledge = appStorage.get('knowledge') || [];
            if (existingKnowledge.length === 0) {
                appStorage.set('knowledge', sampleKnowledge);
            }
        }

        // 初始化分类标签
        function initCategoryTabs() {
            const tabs = document.querySelectorAll('.category-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    currentCategory = this.dataset.category;
                    loadKnowledge();
                });
            });
        }

        // 初始化搜索
        function initSearch() {
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    searchQuery = this.value.trim().toLowerCase();
                    loadKnowledge();
                }, 300);
            });
        }

        // 加载技巧列表
        function loadKnowledge() {
            // 使用新的搜索方法
            const filters = {
                category: currentCategory,
                gameType: getCurrentGameTypeFilter(),
                difficulty: getCurrentDifficultyFilter(),
                favorited: showFavoritesOnly
            };

            const filteredKnowledge = appStorage.searchKnowledge(searchQuery, filters);
            renderKnowledge(filteredKnowledge);
            updateKnowledgeCount(filteredKnowledge.length);
        }

        // 获取当前游戏类型筛选
        function getCurrentGameTypeFilter() {
            const activeFilter = document.querySelector('.game-type-filter.active');
            return activeFilter ? activeFilter.dataset.gameType : 'all';
        }

        // 获取当前难度筛选
        function getCurrentDifficultyFilter() {
            const activeFilter = document.querySelector('.difficulty-filter.active');
            return activeFilter ? activeFilter.dataset.difficulty : 'all';
        }

        // 更新技巧数量显示
        function updateKnowledgeCount(count) {
            const countElement = document.querySelector('.knowledge-count');
            if (countElement) {
                countElement.textContent = `共 ${count} 条技巧`;
            }
        }

        // 渲染技巧列表
        function renderKnowledge(knowledge) {
            const container = document.getElementById('knowledgeList');

            if (knowledge.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <div class="empty-title">暂无技巧</div>
                        <div class="empty-subtitle">
                            ${searchQuery || currentCategory !== 'all' ? '没有找到匹配的技巧' : '开始添加你的第一个技巧吧'}
                        </div>
                        ${!searchQuery && currentCategory === 'all' ? `
                            <button class="button-primary" onclick="Navigator.navigate('add-knowledge.html')">
                                <i class="fas fa-plus"></i> 新增技巧
                            </button>
                        ` : ''}
                    </div>
                `;
                return;
            }

            container.innerHTML = knowledge.map(item => `
                <div class="knowledge-item" onclick="Navigator.navigate('knowledge-detail.html', { id: '${item.id}' })">
                    <div class="knowledge-header">
                        <div>
                            <div class="knowledge-category">${item.category}</div>
                            <div class="knowledge-title">${item.title}</div>
                        </div>
                        <button class="favorite-button ${item.isFavorite ? 'active' : ''}" onclick="event.stopPropagation(); toggleFavorite('${item.id}')">
                            <i class="fas fa-heart"></i>
                        </button>
                    </div>
                    <div class="knowledge-meta">
                        <div class="game-type-badge">
                            <span>${GAME_TYPES[item.gameType]?.icon || '🎯'}</span>
                            <span>${item.gameType}</span>
                        </div>
                        <div class="difficulty-stars">
                            ${Array(item.difficulty || 1).fill('⭐').join('')}
                        </div>
                    </div>
                    <div class="knowledge-content">${item.content}</div>
                    <div class="knowledge-footer">
                        <div class="knowledge-tags">
                            ${item.tags.map(tag => `<span class="knowledge-tag">${tag}</span>`).join('')}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 切换收藏状态
        function toggleFavorite(knowledgeId) {
            const knowledge = appStorage.get('knowledge') || [];
            const item = knowledge.find(k => k.id === knowledgeId);

            if (item) {
                item.isFavorite = !item.isFavorite;
                appStorage.set('knowledge', knowledge);
                loadKnowledge();
                UIUtils.showToast(item.isFavorite ? '已添加到收藏' : '已取消收藏', 'success');
            }
        }

        // 打开精选技巧
        function openFeaturedKnowledge(gameType) {
            const knowledge = appStorage.get('knowledge') || [];
            const featuredItem = knowledge.find(item =>
                item.gameType === gameType &&
                (item.category === '开局' || item.tags.includes('基础'))
            );

            if (featuredItem) {
                Navigator.navigate('knowledge-detail.html', { id: featuredItem.id });
            } else {
                // 如果没有找到对应的技巧，创建一个示例技巧
                createFeaturedKnowledge(gameType);
            }
        }

        // 创建精选技巧示例
        function createFeaturedKnowledge(gameType) {
            const featuredKnowledge = {
                '象棋': {
                    title: '象棋基本开局原则',
                    category: '开局',
                    content: '象棋开局的基本原则包括：\n\n1. 快速出子，占据中心\n开局时应该尽快将子力调动起来，特别是马、炮等重要子力，争取占据棋盘中心的有利位置。\n\n2. 保护主帅安全\n在开局阶段就要注意主帅的安全，避免过早暴露在对方的攻击范围内。\n\n3. 协调各子配合\n各个棋子之间要相互配合，形成有机的整体，避免孤军作战。\n\n4. 避免重复走子\n开局时应该避免同一个棋子反复移动，这样会浪费宝贵的开局时间。\n\n掌握这些基本原则，能够帮助你在开局阶段取得优势，为中局的战斗打下良好基础。',
                    difficulty: 2,
                    tags: ['基础', '开局', '原则']
                },
                '围棋': {
                    title: '围棋基本定式',
                    category: '开局',
                    content: '围棋定式是经过长期实战检验的固定下法，是围棋理论的重要组成部分。\n\n常见定式类型：\n\n1. 星位定式\n在星位（4-4点）的基本应对方法，包括小飞挂、大飞挂等。\n\n2. 小目定式\n在小目（3-4点）的各种变化，是最常用的布局方式。\n\n3. 高目定式\n在高目（4-5点）的应对，通常用于特殊战略需要。\n\n学习定式的意义：\n- 提高布局效率\n- 避免明显错误\n- 理解局部最佳下法\n- 为中盘战斗做准备\n\n建议初学者从基本的星位和小目定式开始学习，逐步掌握更复杂的变化。',
                    difficulty: 3,
                    tags: ['定式', '布局', '基础']
                },
                '扑克': {
                    title: '德州扑克概率计算',
                    category: '技巧',
                    content: '在德州扑克中，概率计算是提高胜率的重要技能。\n\n基本概率概念：\n\n1. 起手牌概率\n- 拿到AA的概率：0.45%\n- 拿到任意对子：5.9%\n- 拿到AK：1.2%\n\n2. 翻牌后概率\n- 同花听牌成牌概率：约35%\n- 顺子听牌成牌概率：约32%\n- 对子成三条概率：约8.5%\n\n3. 底池赔率计算\n底池赔率 = 需要跟注金额 / (底池金额 + 需要跟注金额)\n\n4. 期望值计算\nEV = (获胜概率 × 获胜金额) - (失败概率 × 失败金额)\n\n掌握这些基本概率，能够帮助你做出更理性的决策，长期来看会显著提高你的胜率。',
                    difficulty: 4,
                    tags: ['概率', '策略', '数学']
                }
            };

            const knowledgeData = featuredKnowledge[gameType];
            if (knowledgeData) {
                const newKnowledge = {
                    id: Date.now().toString(),
                    ...knowledgeData,
                    gameType: gameType,
                    isFavorite: false,
                    createdAt: new Date().toISOString()
                };

                const knowledge = appStorage.get('knowledge') || [];
                knowledge.unshift(newKnowledge);
                appStorage.set('knowledge', knowledge);

                Navigator.navigate('knowledge-detail.html', { id: newKnowledge.id });
            }
        }

        // 增强搜索功能
        function enhancedSearch(query) {
            const knowledge = appStorage.get('knowledge') || [];
            const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);

            return knowledge.filter(item => {
                const searchableText = [
                    item.title,
                    item.content,
                    item.category,
                    item.gameType,
                    ...item.tags
                ].join(' ').toLowerCase();

                return searchTerms.every(term => searchableText.includes(term));
            }).sort((a, b) => {
                // 按相关性排序
                const aScore = calculateRelevanceScore(a, searchTerms);
                const bScore = calculateRelevanceScore(b, searchTerms);
                return bScore - aScore;
            });
        }

        // 计算相关性得分
        function calculateRelevanceScore(item, searchTerms) {
            let score = 0;
            const title = item.title.toLowerCase();
            const content = item.content.toLowerCase();

            searchTerms.forEach(term => {
                if (title.includes(term)) score += 10; // 标题匹配权重更高
                if (content.includes(term)) score += 5;
                if (item.tags.some(tag => tag.toLowerCase().includes(term))) score += 8;
                if (item.category.toLowerCase().includes(term)) score += 6;
                if (item.gameType.toLowerCase().includes(term)) score += 7;
            });

            return score;
        }



        // 切换收藏状态
        function toggleFavorite(knowledgeId, event) {
            if (event) {
                event.stopPropagation();
            }

            const updatedKnowledge = appStorage.toggleKnowledgeFavorite(knowledgeId);
            if (updatedKnowledge) {
                const favoriteIcon = document.querySelector(`[data-knowledge-id="${knowledgeId}"] .favorite-icon`);
                if (favoriteIcon) {
                    favoriteIcon.classList.toggle('favorited', updatedKnowledge.isFavorited);
                    favoriteIcon.innerHTML = updatedKnowledge.isFavorited ?
                        '<i class="fas fa-heart"></i>' : '<i class="far fa-heart"></i>';
                }

                UIUtils.showToast(
                    updatedKnowledge.isFavorited ? '已添加到收藏' : '已取消收藏',
                    'success'
                );
            }
        }

        // 快速筛选收藏的技巧
        function showFavorites() {
            showFavoritesOnly = !showFavoritesOnly;
            const button = document.querySelector('.favorites-button');
            if (button) {
                button.classList.toggle('active', showFavoritesOnly);
                button.innerHTML = showFavoritesOnly ?
                    '<i class="fas fa-heart"></i> 显示全部' :
                    '<i class="far fa-heart"></i> 收藏';
            }

            loadKnowledge();

            UIUtils.showToast(
                showFavoritesOnly ? '显示收藏技巧' : '显示全部技巧',
                'info'
            );
        }

        // 查看技巧详情
        function viewKnowledgeDetail(knowledgeId) {
            // 增加浏览次数
            appStorage.incrementKnowledgeViews(knowledgeId);
            Navigator.navigate('knowledge-detail.html', { id: knowledgeId });
        }

        // 按难度筛选
        function filterByDifficulty(difficulty) {
            const knowledge = appStorage.get('knowledge') || [];
            const filtered = knowledge.filter(item => item.difficulty === difficulty);

            currentCategory = 'all';
            searchQuery = '';
            document.getElementById('searchInput').value = '';

            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector('.category-tab[data-category="all"]').classList.add('active');

            renderKnowledge(filtered);
            UIUtils.showToast(`找到 ${filtered.length} 个${difficulty}星难度的技巧`, 'success');
        }

        // 随机推荐技巧
        function randomRecommendation() {
            const knowledge = appStorage.get('knowledge') || [];
            if (knowledge.length === 0) {
                UIUtils.showToast('暂无技巧可推荐', 'info');
                return;
            }

            const randomIndex = Math.floor(Math.random() * knowledge.length);
            const randomKnowledge = knowledge[randomIndex];

            Navigator.navigate('knowledge-detail.html', { id: randomKnowledge.id });
        }

        // 键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + F 聚焦搜索框
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                document.getElementById('searchInput').focus();
            }

            // Ctrl/Cmd + N 新增技巧
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                Navigator.navigate('add-knowledge.html');
            }

            // Ctrl/Cmd + H 显示收藏
            if ((e.ctrlKey || e.metaKey) && e.key === 'h') {
                e.preventDefault();
                showFavorites();
            }

            // Ctrl/Cmd + R 随机推荐
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                randomRecommendation();
            }

            // 数字键1-5快速筛选难度
            if (e.key >= '1' && e.key <= '5' && !e.ctrlKey && !e.metaKey) {
                const searchInput = document.getElementById('searchInput');
                if (document.activeElement !== searchInput) {
                    e.preventDefault();
                    filterByDifficulty(parseInt(e.key));
                }
            }
        });

        // 长按收藏按钮显示更多选项
        let longPressTimer = null;

        function handleFavoritePress(knowledgeId, event) {
            event.stopPropagation();

            longPressTimer = setTimeout(() => {
                showKnowledgeOptions(knowledgeId);
            }, 500);
        }

        function handleFavoriteRelease(knowledgeId, event) {
            event.stopPropagation();

            if (longPressTimer) {
                clearTimeout(longPressTimer);
                longPressTimer = null;
                toggleFavorite(knowledgeId);
            }
        }

        // 显示技巧选项菜单
        function showKnowledgeOptions(knowledgeId) {
            const knowledge = appStorage.get('knowledge') || [];
            const item = knowledge.find(k => k.id === knowledgeId);

            if (!item) return;

            const options = [
                {
                    text: '查看详情',
                    icon: 'fas fa-eye',
                    action: () => Navigator.navigate('knowledge-detail.html', { id: knowledgeId })
                },
                {
                    text: '编辑技巧',
                    icon: 'fas fa-edit',
                    action: () => Navigator.navigate('add-knowledge.html', { edit: 'true', id: knowledgeId })
                },
                {
                    text: item.isFavorite ? '取消收藏' : '添加收藏',
                    icon: item.isFavorite ? 'fas fa-heart-broken' : 'fas fa-heart',
                    action: () => toggleFavorite(knowledgeId)
                },
                {
                    text: '应用到记录',
                    icon: 'fas fa-plus-circle',
                    action: () => Navigator.navigate('add-record.html', {
                        gameType: item.gameType,
                        notes: `参考技巧: ${item.title}`,
                        tags: item.tags.join(',')
                    })
                }
            ];

            UIUtils.showActionSheet(item.title, options);
        }

        // 自定义新增技巧
        function addCustomKnowledge() {
            // 添加点击反馈效果
            const button = document.querySelector('.custom-add-button');
            button.style.transform = 'scale(0.95)';

            setTimeout(() => {
                button.style.transform = '';
            }, 150);

            // 显示提示信息
            UIUtils.showToast('正在打开新增技巧页面...', 'info');

            // 跳转到新增技巧页面
            setTimeout(() => {
                Navigator.navigate('add-knowledge.html');
            }, 200);

            // 记录用户行为统计（可选）
            try {
                const stats = appStorage.get('userStats') || {};
                stats.customAddClicks = (stats.customAddClicks || 0) + 1;
                stats.lastCustomAddTime = new Date().toISOString();
                appStorage.set('userStats', stats);
            } catch (error) {
                console.log('统计记录失败:', error);
            }
        }

        // 添加统计信息显示
        function showKnowledgeStats() {
            const knowledge = appStorage.get('knowledge') || [];

            const stats = {
                total: knowledge.length,
                favorites: knowledge.filter(k => k.isFavorite).length,
                byCategory: {},
                byGameType: {},
                byDifficulty: {}
            };

            knowledge.forEach(item => {
                stats.byCategory[item.category] = (stats.byCategory[item.category] || 0) + 1;
                stats.byGameType[item.gameType] = (stats.byGameType[item.gameType] || 0) + 1;
                stats.byDifficulty[item.difficulty] = (stats.byDifficulty[item.difficulty] || 0) + 1;
            });

            const message = `
                技巧库统计信息：

                总技巧数：${stats.total}
                收藏数：${stats.favorites}

                分类分布：
                ${Object.entries(stats.byCategory).map(([cat, count]) => `${cat}: ${count}`).join('\n')}

                游戏类型分布：
                ${Object.entries(stats.byGameType).map(([type, count]) => `${type}: ${count}`).join('\n')}
            `;

            UIUtils.showDialog('技巧库统计', message, [
                { text: '确定', action: () => {} }
            ]);
        }
    </script>
</body>
</html>
