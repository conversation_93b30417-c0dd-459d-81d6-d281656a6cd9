<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对局记录</title>
    <link rel="stylesheet" href="ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .search-bar {
            padding: var(--spacing-md);
            background: var(--background-color);
        }

        .search-input-container {
            position: relative;
            background: white;
            border-radius: var(--radius-lg);
            border: 1px solid var(--ios-gray4);
            display: flex;
            align-items: center;
            padding: var(--spacing-sm) var(--spacing-md);
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            padding: var(--spacing-sm) 0;
            background: transparent;
        }

        .search-icon {
            color: var(--ios-gray);
            margin-right: var(--spacing-sm);
        }

        .filter-button {
            background: var(--ios-blue);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            padding: var(--spacing-sm) var(--spacing-md);
            margin-left: var(--spacing-sm);
            cursor: pointer;
        }

        .filter-tabs {
            display: flex;
            padding: 0 var(--spacing-md);
            gap: var(--spacing-sm);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .filter-tabs::-webkit-scrollbar {
            display: none;
        }

        .filter-tab {
            background: white;
            border: 1px solid var(--ios-gray4);
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-tab.active {
            background: var(--ios-blue);
            color: white;
            border-color: var(--ios-blue);
        }

        /* 批量操作样式 */
        .batch-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md);
            background: var(--ios-blue);
            color: white;
            margin: 0 var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-md);
        }

        .batch-info {
            font-weight: 600;
        }

        .batch-buttons {
            display: flex;
            gap: var(--spacing-sm);
        }

        .batch-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-sm);
            font-size: 14px;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .batch-button:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .batch-button.delete {
            background: var(--ios-red);
        }

        .batch-button.delete:hover {
            background: #E02424;
        }

        /* 批量模式记录项样式 */
        .record-item.batch-mode {
            padding-left: 50px;
            position: relative;
        }

        .record-checkbox-container {
            position: absolute;
            left: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
        }

        .record-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--ios-blue);
        }

        /* 排序选项样式 */
        .sort-options {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .sort-option {
            padding: var(--spacing-md);
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: background 0.2s;
            border: 1px solid var(--ios-gray5);
        }

        .sort-option:hover {
            background: var(--ios-gray6);
        }

        .sort-option.active {
            background: var(--ios-blue);
            color: white;
            border-color: var(--ios-blue);
        }

        /* 按钮样式增强 */
        .sort-button, .filter-button {
            background: var(--ios-gray6);
            border: none;
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: all 0.2s;
            margin-left: var(--spacing-sm);
        }

        .sort-button:hover, .filter-button:hover {
            background: var(--ios-gray5);
            transform: scale(1.05);
        }

        .records-list {
            padding: var(--spacing-md);
        }

        .record-item {
            background: white;
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
            cursor: pointer;
            transition: all 0.2s;
        }

        .record-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-sm);
        }

        .record-title {
            flex: 1;
        }

        .game-type-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            background: var(--ios-gray6);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }

        .opponent-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .record-result {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 600;
            color: white;
            text-align: center;
            min-width: 50px;
        }

        .record-result.win { background: var(--ios-green); }
        .record-result.lose { background: var(--ios-red); }
        .record-result.draw { background: var(--ios-orange); }

        .record-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-sm);
        }

        .record-date {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .record-duration {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .record-notes {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.4;
            margin-bottom: var(--spacing-sm);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .record-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }

        .record-tag {
            background: var(--ios-blue);
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 500;
        }

        .importance-stars {
            display: flex;
            gap: 2px;
            margin-top: var(--spacing-xs);
        }

        .importance-star {
            color: var(--secondary-color);
            font-size: 12px;
        }

        .empty-state {
            text-align: center;
            padding: var(--spacing-xl) var(--spacing-md);
            color: var(--text-secondary);
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: var(--spacing-md);
            opacity: 0.3;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .empty-subtitle {
            font-size: 14px;
            margin-bottom: var(--spacing-lg);
        }

        .floating-add {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: var(--ios-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-lg);
            cursor: pointer;
            transition: all 0.2s;
            z-index: 50;
        }

        .floating-add:hover {
            transform: scale(1.1);
        }

        .floating-add i {
            color: white;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                    </div>
                    <div class="wifi-icon"></div>
                    <span>100%</span>
                    <div class="battery-icon"></div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <div class="nav-title">对局记录</div>
                <button class="nav-button" onclick="Navigator.navigate('search.html')">
                    <i class="fas fa-search"></i>
                </button>
            </div>

            <!-- 搜索栏 -->
            <div class="search-bar">
                <div class="search-input-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="搜索对手、笔记内容..." id="searchInput">
                    <button class="filter-button" onclick="showFilterOptions()">
                        <i class="fas fa-filter" id="filterIcon"></i>
                    </button>
                    <button class="sort-button" onclick="showSortOptions()">
                        <i class="fas fa-sort" id="sortIcon"></i>
                    </button>
                </div>
            </div>

            <!-- 筛选标签 -->
            <div class="filter-tabs" id="filterTabs">
                <div class="filter-tab active" data-filter="all">全部</div>
                <div class="filter-tab" data-filter="象棋">象棋</div>
                <div class="filter-tab" data-filter="围棋">围棋</div>
                <div class="filter-tab" data-filter="国际象棋">国际象棋</div>
                <div class="filter-tab" data-filter="扑克">扑克</div>
                <div class="filter-tab" data-filter="麻将">麻将</div>
            </div>

            <!-- 批量操作栏 -->
            <div class="batch-actions" id="batchActions" style="display: none;">
                <div class="batch-info">
                    已选择 <span id="selectedCount">0</span> 项
                </div>
                <div class="batch-buttons">

                    <button class="batch-button delete" onclick="deleteSelected()">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                    <button class="batch-button" onclick="cancelBatchMode()">
                        取消
                    </button>
                </div>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <div class="records-list" id="recordsList">
                    <!-- 动态加载记录列表 -->
                </div>
            </div>

            <!-- 底部标签栏 -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item" data-tab="home">
                    <div class="tab-icon"><i class="fas fa-home"></i></div>
                    <div class="tab-label">首页</div>
                </a>
                <a href="records.html" class="tab-item active" data-tab="records">
                    <div class="tab-icon"><i class="fas fa-list"></i></div>
                    <div class="tab-label">记录</div>
                </a>
                <a href="statistics.html" class="tab-item" data-tab="statistics">
                    <div class="tab-icon"><i class="fas fa-chart-bar"></i></div>
                    <div class="tab-label">统计</div>
                </a>
                <a href="knowledge.html" class="tab-item" data-tab="knowledge">
                    <div class="tab-icon"><i class="fas fa-book"></i></div>
                    <div class="tab-label">技巧</div>
                </a>
            </div>

            <!-- 悬浮添加按钮 -->
            <div class="floating-add" onclick="Navigator.navigate('add-record.html')">
                <i class="fas fa-plus"></i>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="app-utils.js"></script>
    <script>
        let currentFilter = 'all';
        let searchQuery = '';
        let currentSort = 'date-desc';
        let batchMode = false;
        let selectedRecords = new Set();

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initFilterTabs();
            initSearch();
            initBatchMode();

            // 检查是否有预设的对手筛选
            const params = Navigator.getParams();
            if (params.opponent) {
                searchQuery = params.opponent.toLowerCase();
                document.getElementById('searchInput').value = params.opponent;
                UIUtils.showToast(`已筛选对手：${params.opponent}`, 'info');
            }

            loadRecords();
        });

        // 初始化筛选标签
        function initFilterTabs() {
            const tabs = document.querySelectorAll('.filter-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    currentFilter = this.dataset.filter;
                    loadRecords();
                });
            });
        }

        // 初始化搜索
        function initSearch() {
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    searchQuery = this.value.trim().toLowerCase();
                    loadRecords();
                }, 300);
            });
        }

        // 加载记录列表
        function loadRecords() {
            // 使用新的搜索方法
            const filters = {
                gameType: currentFilter,
                result: getResultFilter()
            };

            const filteredRecords = appStorage.searchRecords(searchQuery, filters);

            // 排序
            const sortedRecords = sortRecords(filteredRecords);

            renderRecords(sortedRecords);
            updateRecordCount(sortedRecords.length);
        }

        // 获取结果筛选
        function getResultFilter() {
            const activeResultFilter = document.querySelector('.result-filter.active');
            return activeResultFilter ? activeResultFilter.dataset.result : 'all';
        }

        // 更新记录数量显示
        function updateRecordCount(count) {
            const countElement = document.querySelector('.records-count');
            if (countElement) {
                countElement.textContent = `共 ${count} 条记录`;
            }
        }

        // 排序记录
        function sortRecords(records) {
            return records.sort((a, b) => {
                switch (currentSort) {
                    case 'date-asc':
                        return new Date(a.date) - new Date(b.date);
                    case 'date-desc':
                        return new Date(b.date) - new Date(a.date);
                    case 'result-win':
                        if (a.result === '胜' && b.result !== '胜') return -1;
                        if (a.result !== '胜' && b.result === '胜') return 1;
                        return new Date(b.date) - new Date(a.date);
                    case 'result-lose':
                        if (a.result === '负' && b.result !== '负') return -1;
                        if (a.result !== '负' && b.result === '负') return 1;
                        return new Date(b.date) - new Date(a.date);
                    case 'importance':
                        const importanceOrder = { '高': 3, '中': 2, '低': 1 };
                        const aImportance = importanceOrder[a.importance] || 0;
                        const bImportance = importanceOrder[b.importance] || 0;
                        if (aImportance !== bImportance) return bImportance - aImportance;
                        return new Date(b.date) - new Date(a.date);
                    default:
                        return new Date(b.date) - new Date(a.date);
                }
            });
        }

        // 渲染记录列表
        function renderRecords(records) {
            const container = document.getElementById('recordsList');

            if (records.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-chess-board"></i>
                        </div>
                        <div class="empty-title">暂无记录</div>
                        <div class="empty-subtitle">
                            ${searchQuery || currentFilter !== 'all' ? '没有找到匹配的记录' : '开始记录你的第一局对弈吧'}
                        </div>
                        ${!searchQuery && currentFilter === 'all' ? `
                            <button class="button-primary" onclick="Navigator.navigate('add-record.html')">
                                <i class="fas fa-plus"></i> 新增记录
                            </button>
                        ` : ''}
                    </div>
                `;
                return;
            }

            container.innerHTML = records.map(record => `
                <div class="record-item ${batchMode ? 'batch-mode' : ''}" data-record-id="${record.id}"
                     onclick="${batchMode ? `toggleRecordSelection('${record.id}')` : `Navigator.navigate('record-detail.html', {id: '${record.id}'})`}">
                    ${batchMode ? `
                        <div class="record-checkbox-container">
                            <input type="checkbox" class="record-checkbox"
                                   onchange="toggleRecordSelection('${record.id}')"
                                   ${selectedRecords.has(record.id) ? 'checked' : ''}>
                        </div>
                    ` : ''}
                    <div class="record-header">
                        <div class="record-title">
                            <div class="game-type-badge">
                                <span>${GAME_TYPES[record.gameType]?.icon || '🎯'}</span>
                                <span>${record.gameType}</span>
                            </div>
                            <div class="opponent-name">vs ${record.opponent}</div>
                            ${record.importance > 1 ? `
                                <div class="importance-stars">
                                    ${Array(record.importance).fill('⭐').join('')}
                                </div>
                            ` : ''}
                        </div>
                        <div class="record-result ${record.result === '胜' ? 'win' : record.result === '负' ? 'lose' : 'draw'}">
                            ${record.result}
                        </div>
                    </div>
                    <div class="record-meta">
                        <div class="record-date">
                            <i class="fas fa-calendar"></i>
                            <span>${DateUtils.formatDate(record.date, 'MM-DD HH:mm')}</span>
                        </div>
                        ${record.duration ? `
                            <div class="record-duration">
                                <i class="fas fa-clock"></i>
                                <span>${record.duration}</span>
                            </div>
                        ` : ''}
                    </div>
                    ${record.notes ? `
                        <div class="record-notes">${record.notes}</div>
                    ` : ''}
                    ${record.tags && record.tags.length > 0 ? `
                        <div class="record-tags">
                            ${record.tags.map(tag => `<span class="record-tag">${tag}</span>`).join('')}
                        </div>
                    ` : ''}
                </div>
            `).join('');
        }

        // 显示筛选选项
        function showFilterOptions() {
            const filterIcon = document.getElementById('filterIcon');
            filterIcon.classList.add('fa-spin');

            setTimeout(() => {
                filterIcon.classList.remove('fa-spin');
                showAdvancedFilter();
            }, 500);
        }

        // 显示高级筛选对话框
        function showAdvancedFilter() {
            const gameTypes = Object.keys(GAME_TYPES);
            const results = ['胜利', '失败', '平局'];
            const importanceLevels = [1, 2, 3, 4, 5];

            const filterOptions = [
                {
                    text: '按游戏类型筛选',
                    icon: 'fas fa-gamepad',
                    action: () => showGameTypeFilter(gameTypes)
                },
                {
                    text: '按对局结果筛选',
                    icon: 'fas fa-trophy',
                    action: () => showResultFilter(results)
                },
                {
                    text: '按重要程度筛选',
                    icon: 'fas fa-star',
                    action: () => showImportanceFilter(importanceLevels)
                },
                {
                    text: '按时间范围筛选',
                    icon: 'fas fa-calendar',
                    action: () => showDateRangeFilter()
                },
                {
                    text: '清除所有筛选',
                    icon: 'fas fa-times',
                    action: () => clearAllFilters()
                }
            ];

            UIUtils.showActionSheet('高级筛选', filterOptions);
        }

        // 按游戏类型筛选
        function showGameTypeFilter(gameTypes) {
            const options = gameTypes.map(type => ({
                text: `${GAME_TYPES[type].icon} ${type}`,
                action: () => {
                    currentFilter = 'all';
                    searchQuery = '';
                    document.getElementById('searchInput').value = '';

                    const records = appStorage.get('records') || [];
                    const filtered = records.filter(record => record.gameType === type);
                    renderRecords(filtered);
                    UIUtils.showToast(`显示${type}记录 (${filtered.length}条)`, 'success');
                }
            }));

            UIUtils.showActionSheet('选择游戏类型', options);
        }

        // 按对局结果筛选
        function showResultFilter(results) {
            const options = results.map(result => ({
                text: result,
                action: () => {
                    currentFilter = 'all';
                    searchQuery = '';
                    document.getElementById('searchInput').value = '';

                    const records = appStorage.get('records') || [];
                    const filtered = records.filter(record => record.result === result);
                    renderRecords(filtered);
                    UIUtils.showToast(`显示${result}记录 (${filtered.length}条)`, 'success');
                }
            }));

            UIUtils.showActionSheet('选择对局结果', options);
        }

        // 按重要程度筛选
        function showImportanceFilter(levels) {
            const options = levels.map(level => ({
                text: `${'⭐'.repeat(level)} ${level}星`,
                action: () => {
                    currentFilter = 'all';
                    searchQuery = '';
                    document.getElementById('searchInput').value = '';

                    const records = appStorage.get('records') || [];
                    const filtered = records.filter(record => record.importance === level);
                    renderRecords(filtered);
                    UIUtils.showToast(`显示${level}星重要度记录 (${filtered.length}条)`, 'success');
                }
            }));

            UIUtils.showActionSheet('选择重要程度', options);
        }

        // 按时间范围筛选
        function showDateRangeFilter() {
            const today = new Date();
            const ranges = [
                {
                    text: '今天',
                    action: () => filterByDateRange(today, today)
                },
                {
                    text: '最近7天',
                    action: () => {
                        const weekAgo = new Date(today);
                        weekAgo.setDate(weekAgo.getDate() - 7);
                        filterByDateRange(weekAgo, today);
                    }
                },
                {
                    text: '最近30天',
                    action: () => {
                        const monthAgo = new Date(today);
                        monthAgo.setDate(monthAgo.getDate() - 30);
                        filterByDateRange(monthAgo, today);
                    }
                },
                {
                    text: '最近3个月',
                    action: () => {
                        const threeMonthsAgo = new Date(today);
                        threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
                        filterByDateRange(threeMonthsAgo, today);
                    }
                }
            ];

            UIUtils.showActionSheet('选择时间范围', ranges);
        }

        // 按时间范围筛选记录
        function filterByDateRange(startDate, endDate) {
            currentFilter = 'all';
            searchQuery = '';
            document.getElementById('searchInput').value = '';

            const records = appStorage.get('records') || [];
            const filtered = records.filter(record => {
                const recordDate = new Date(record.date);
                return recordDate >= startDate && recordDate <= endDate;
            });

            renderRecords(filtered);
            const startStr = DateUtils.formatDate(startDate);
            const endStr = DateUtils.formatDate(endDate);
            UIUtils.showToast(`显示${startStr}至${endStr}的记录 (${filtered.length}条)`, 'success');
        }

        // 清除所有筛选
        function clearAllFilters() {
            currentFilter = 'all';
            searchQuery = '';
            document.getElementById('searchInput').value = '';

            // 重置筛选标签状态
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector('.filter-tab[data-filter="all"]').classList.add('active');

            loadRecords();
            UIUtils.showToast('已清除所有筛选条件', 'success');
        }

        // 显示排序选项
        function showSortOptions() {
            const options = [
                { value: 'date-desc', label: '按日期降序' },
                { value: 'date-asc', label: '按日期升序' },
                { value: 'result-win', label: '胜局优先' },
                { value: 'result-lose', label: '败局优先' },
                { value: 'importance', label: '按重要性' }
            ];

            const optionsHtml = options.map(option =>
                `<div class="sort-option ${option.value === currentSort ? 'active' : ''}"
                      onclick="setSortOption('${option.value}')">${option.label}</div>`
            ).join('');

            UIUtils.showDialog('选择排序方式', `<div class="sort-options">${optionsHtml}</div>`, [
                { text: '取消', action: () => {} }
            ]);
        }

        // 设置排序选项
        function setSortOption(sortType) {
            currentSort = sortType;
            loadRecords();
            UIUtils.hideDialog();
            UIUtils.showToast(`已按${getSortLabel(sortType)}排序`, 'success');
        }

        // 获取排序标签
        function getSortLabel(sortType) {
            const labels = {
                'date-desc': '日期降序',
                'date-asc': '日期升序',
                'result-win': '胜局优先',
                'result-lose': '败局优先',
                'importance': '重要性'
            };
            return labels[sortType] || '日期降序';
        }

        // 初始化批量模式
        function initBatchMode() {
            // 长按进入批量选择模式
            let longPressTimer;
            document.addEventListener('touchstart', function(e) {
                if (e.target.closest('.record-item')) {
                    longPressTimer = setTimeout(() => {
                        enterBatchMode();
                    }, 800);
                }
            });

            document.addEventListener('touchend', function() {
                clearTimeout(longPressTimer);
            });
        }

        // 进入批量模式
        function enterBatchMode() {
            batchMode = true;
            selectedRecords.clear();
            document.getElementById('batchActions').style.display = 'flex';
            UIUtils.showToast('已进入批量选择模式', 'info');
            loadRecords(); // 重新渲染以显示复选框
        }

        // 退出批量模式
        function cancelBatchMode() {
            batchMode = false;
            selectedRecords.clear();
            document.getElementById('batchActions').style.display = 'none';
            loadRecords(); // 重新渲染以隐藏复选框
        }

        // 切换记录选择状态
        function toggleRecordSelection(recordId) {
            if (selectedRecords.has(recordId)) {
                selectedRecords.delete(recordId);
            } else {
                selectedRecords.add(recordId);
            }
            updateBatchUI();
        }

        // 更新批量操作UI
        function updateBatchUI() {
            document.getElementById('selectedCount').textContent = selectedRecords.size;

            // 更新复选框状态
            selectedRecords.forEach(id => {
                const checkbox = document.querySelector(`[data-record-id="${id}"] .record-checkbox`);
                if (checkbox) checkbox.checked = true;
            });
        }



        // 删除选中记录
        function deleteSelected() {
            if (selectedRecords.size === 0) {
                UIUtils.showToast('请先选择要删除的记录', 'warning');
                return;
            }

            UIUtils.showConfirm(
                `确定要删除选中的${selectedRecords.size}条记录吗？`,
                '此操作不可撤销',
                () => {
                    const recordIds = Array.from(selectedRecords);
                    const deletedRecords = appStorage.deleteRecords(recordIds);

                    if (deletedRecords.length > 0) {
                        UIUtils.showToast(`已删除${deletedRecords.length}条记录`, 'success');
                        cancelBatchMode();
                        loadRecords();
                    } else {
                        UIUtils.showToast('删除失败', 'error');
                    }
                }
            );
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const allRecords = document.querySelectorAll('.record-item[data-record-id]');
            const allSelected = selectedRecords.size === allRecords.length;

            if (allSelected) {
                selectedRecords.clear();
            } else {
                allRecords.forEach(item => {
                    selectedRecords.add(item.dataset.recordId);
                });
            }

            updateBatchUI();
        }

        // 导出选中记录
        function exportSelected() {
            if (selectedRecords.size === 0) {
                UIUtils.showToast('请先选择要导出的记录', 'warning');
                return;
            }

            const records = appStorage.get('gameRecords') || [];
            const selectedRecordData = records.filter(record => selectedRecords.has(record.id));

            const exportData = {
                exportDate: new Date().toISOString(),
                recordCount: selectedRecordData.length,
                records: selectedRecordData
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `chess_records_${DateUtils.formatDate(new Date(), 'YYYY-MM-DD')}.json`;
            link.click();

            UIUtils.showToast(`已导出${selectedRecords.size}条记录`, 'success');
        }
    </script>
</body>
</html>
