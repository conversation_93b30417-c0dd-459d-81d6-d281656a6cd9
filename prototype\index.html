<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>棋牌记事本</title>
    <link rel="stylesheet" href="css/ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 首页特定样式 */
        .welcome-section {
            background: linear-gradient(135deg, var(--primary-color), #3B82F6);
            color: white;
            padding: 24px 16px;
            margin: 16px;
            border-radius: var(--border-radius-large);
        }
        
        .welcome-title {
            font-size: var(--font-size-title2);
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .welcome-subtitle {
            font-size: var(--font-size-subhead);
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin: 16px;
        }
        
        .stat-card {
            background: var(--surface-color);
            border-radius: var(--border-radius-medium);
            padding: 16px;
            text-align: center;
            box-shadow: var(--shadow-small);
        }
        
        .stat-number {
            font-size: var(--font-size-title1);
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-caption1);
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .quick-actions {
            margin: 16px;
        }
        
        .section-title {
            font-size: var(--font-size-title3);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            padding: 0 4px;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }
        
        .action-btn {
            background: var(--surface-color);
            border: none;
            border-radius: var(--border-radius-medium);
            padding: 20px 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all var(--animation-fast);
            box-shadow: var(--shadow-small);
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }
        
        .action-btn-icon {
            font-size: 24px;
            color: var(--primary-color);
        }
        
        .action-btn-text {
            font-size: var(--font-size-subhead);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .recent-section {
            margin: 16px;
        }
        
        .recent-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 0 4px;
        }
        
        .view-all-btn {
            color: var(--ios-blue);
            font-size: var(--font-size-subhead);
            text-decoration: none;
            font-weight: 500;
        }
        
        .recent-list {
            background: var(--surface-color);
            border-radius: var(--border-radius-medium);
            overflow: hidden;
            box-shadow: var(--shadow-small);
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }
        
        .empty-icon {
            font-size: 48px;
            color: var(--ios-gray3);
            margin-bottom: 16px;
        }
        
        .empty-text {
            font-size: var(--font-size-body);
            margin-bottom: 8px;
        }
        
        .empty-subtext {
            font-size: var(--font-size-subhead);
            color: var(--text-tertiary);
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="device-screen">
            <!-- 首页 -->
            <div id="home" class="page">
                <!-- 状态栏 -->
                <div class="status-bar">
                    <div class="status-left">
                        <span class="status-time">14:30</span>
                    </div>
                    <div class="status-center">
                        <i class="fas fa-signal" style="font-size: 10px;"></i>
                        <i class="fas fa-wifi" style="font-size: 10px; margin-left: 4px;"></i>
                    </div>
                    <div class="status-right">
                        <i class="fas fa-battery-three-quarters" style="font-size: 12px;"></i>
                        <span style="font-size: 11px; margin-left: 2px;">85%</span>
                    </div>
                </div>

                <!-- 导航栏 -->
                <div class="nav-bar">
                    <button class="nav-button" data-action="search">
                        <i class="fas fa-search"></i>
                    </button>
                    <div class="nav-title">棋牌记事本</div>
                    <button class="nav-button primary" data-action="add-record">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>

                <!-- 主内容 -->
                <div class="main-content">
                    <!-- 欢迎区域 -->
                    <div class="welcome-section">
                        <div class="welcome-title">欢迎回来！</div>
                        <div class="welcome-subtitle">继续您的棋牌学习之旅</div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="total-games">0</div>
                            <div class="stat-label">总对局</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="win-rate">0%</div>
                            <div class="stat-label">胜率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="total-opponents">0</div>
                            <div class="stat-label">对手数</div>
                        </div>
                    </div>

                    <!-- 快捷操作 -->
                    <div class="quick-actions">
                        <div class="section-title">快捷操作</div>
                        <div class="action-buttons">
                            <button class="action-btn" data-action="add-record">
                                <div class="action-btn-icon">
                                    <i class="fas fa-plus-circle"></i>
                                </div>
                                <div class="action-btn-text">新建记录</div>
                            </button>
                            <button class="action-btn" data-action="quick-record">
                                <div class="action-btn-icon">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <div class="action-btn-text">快速记录</div>
                            </button>
                            <button class="action-btn" data-action="view-all-records">
                                <div class="action-btn-icon">
                                    <i class="fas fa-list"></i>
                                </div>
                                <div class="action-btn-text">查看记录</div>
                            </button>
                            <button class="action-btn" data-action="add-knowledge">
                                <div class="action-btn-icon">
                                    <i class="fas fa-lightbulb"></i>
                                </div>
                                <div class="action-btn-text">添加技巧</div>
                            </button>
                        </div>
                    </div>

                    <!-- 最近记录 -->
                    <div class="recent-section">
                        <div class="recent-header">
                            <div class="section-title">最近记录</div>
                            <a href="#" class="view-all-btn" data-action="view-all-records">查看全部</a>
                        </div>
                        <div class="recent-list" id="recent-records">
                            <!-- 动态加载最近记录 -->
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <i class="fas fa-chess-board"></i>
                                </div>
                                <div class="empty-text">暂无对局记录</div>
                                <div class="empty-subtext">点击上方按钮开始记录您的第一局对局</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标签栏 -->
                <div class="tab-bar">
                    <div class="tab-item active" data-page="home">
                        <div class="tab-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="tab-label">首页</div>
                    </div>
                    <div class="tab-item" data-page="records">
                        <div class="tab-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="tab-label">记录</div>
                    </div>
                    <div class="tab-item" data-page="stats">
                        <div class="tab-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="tab-label">统计</div>
                    </div>
                    <div class="tab-item" data-page="knowledge">
                        <div class="tab-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="tab-label">技巧</div>
                    </div>
                    <div class="tab-item" data-page="profile">
                        <div class="tab-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="tab-label">我的</div>
                    </div>
                </div>

                <!-- Home Indicator -->
                <div class="home-indicator"></div>
            </div>

            <!-- 记录页面 -->
            <iframe id="records" class="page hidden" src="records.html" style="width: 100%; height: 100%; border: none;"></iframe>

            <!-- 统计页面 -->
            <iframe id="stats" class="page hidden" src="stats.html" style="width: 100%; height: 100%; border: none;"></iframe>

            <!-- 技巧页面 -->
            <iframe id="knowledge" class="page hidden" src="knowledge.html" style="width: 100%; height: 100%; border: none;"></iframe>

            <!-- 个人页面 -->
            <iframe id="profile" class="page hidden" src="profile.html" style="width: 100%; height: 100%; border: none;"></iframe>

            <!-- 新建记录页面 -->
            <iframe id="add-record" class="page hidden" src="add-record.html" style="width: 100%; height: 100%; border: none;"></iframe>
        </div>
    </div>

    <script src="js/app.js"></script>
</body>
</html>
