<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>新建记录 - 棋牌记事本</title>
    <link rel="stylesheet" href="css/ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 新建记录页面特定样式 */
        .form-container {
            padding: 16px;
        }
        
        .form-section {
            background: var(--surface-color);
            border-radius: var(--border-radius-medium);
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: var(--shadow-small);
        }
        
        .section-title {
            font-size: var(--font-size-headline);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-icon {
            color: var(--primary-color);
            font-size: 18px;
        }
        
        .form-row {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        
        .game-type-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .game-type-btn {
            background: var(--ios-gray6);
            border: none;
            border-radius: var(--border-radius-small);
            padding: 12px 8px;
            font-size: var(--font-size-subhead);
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--animation-fast);
            text-align: center;
        }
        
        .game-type-btn.selected {
            background: var(--primary-color);
            color: white;
        }
        
        .game-type-btn:hover {
            background: var(--ios-gray5);
        }
        
        .game-type-btn.selected:hover {
            background: #1E40AF;
        }
        
        .result-options {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .result-btn {
            flex: 1;
            background: var(--ios-gray6);
            border: none;
            border-radius: var(--border-radius-small);
            padding: 12px;
            font-size: var(--font-size-body);
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--animation-fast);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }
        
        .result-btn.selected {
            color: white;
        }
        
        .result-btn.win.selected {
            background: var(--success-color);
        }
        
        .result-btn.loss.selected {
            background: var(--error-color);
        }
        
        .result-btn.draw.selected {
            background: var(--warning-color);
        }
        
        .datetime-input {
            display: flex;
            gap: 8px;
        }
        
        .datetime-input input {
            flex: 1;
        }
        
        .tag-input-container {
            position: relative;
        }
        
        .tag-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-small);
            box-shadow: var(--shadow-medium);
            max-height: 150px;
            overflow-y: auto;
            z-index: 100;
            display: none;
        }
        
        .tag-suggestion {
            padding: 8px 12px;
            cursor: pointer;
            font-size: var(--font-size-subhead);
            color: var(--text-primary);
            border-bottom: 1px solid var(--ios-gray5);
        }
        
        .tag-suggestion:last-child {
            border-bottom: none;
        }
        
        .tag-suggestion:hover {
            background: var(--ios-gray6);
        }
        
        .selected-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }
        
        .tag-chip {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-caption1);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .tag-remove {
            cursor: pointer;
            font-size: 10px;
            opacity: 0.8;
        }
        
        .tag-remove:hover {
            opacity: 1;
        }
        
        .importance-slider {
            margin: 16px 0;
        }
        
        .slider-container {
            position: relative;
            margin: 12px 0;
        }
        
        .slider {
            width: 100%;
            height: 4px;
            border-radius: 2px;
            background: var(--ios-gray5);
            outline: none;
            -webkit-appearance: none;
        }
        
        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .slider-labels {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-caption1);
            color: var(--text-tertiary);
            margin-top: 8px;
        }
        
        .photo-upload {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-medium);
            padding: 24px;
            text-align: center;
            cursor: pointer;
            transition: all var(--animation-fast);
            margin-bottom: 16px;
        }
        
        .photo-upload:hover {
            border-color: var(--primary-color);
            background: var(--ios-gray6);
        }
        
        .photo-upload-icon {
            font-size: 32px;
            color: var(--ios-gray3);
            margin-bottom: 8px;
        }
        
        .photo-upload-text {
            font-size: var(--font-size-subhead);
            color: var(--text-secondary);
        }
        
        .uploaded-photos {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-top: 12px;
        }
        
        .uploaded-photo {
            aspect-ratio: 1;
            background: var(--ios-gray5);
            border-radius: var(--border-radius-small);
            position: relative;
            overflow: hidden;
        }
        
        .photo-remove {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 20px;
            height: 20px;
            background: rgba(0, 0, 0, 0.6);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .save-buttons {
            position: sticky;
            bottom: 0;
            background: var(--background-color);
            padding: 16px;
            border-top: 0.5px solid var(--border-color);
            display: flex;
            gap: 12px;
        }
        
        .btn-cancel {
            flex: 1;
            background: var(--ios-gray6);
            color: var(--text-primary);
        }
        
        .btn-save {
            flex: 2;
            background: var(--primary-color);
            color: white;
        }
        
        .quick-fill {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .quick-fill-btn {
            background: var(--ios-gray6);
            border: none;
            border-radius: 16px;
            padding: 6px 12px;
            font-size: var(--font-size-caption1);
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--animation-fast);
        }
        
        .quick-fill-btn:hover {
            background: var(--ios-gray5);
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="device-screen">
            <!-- 新建记录页面 -->
            <div id="add-record" class="page">
                <!-- 状态栏 -->
                <div class="status-bar">
                    <div class="status-left">
                        <span class="status-time">14:30</span>
                    </div>
                    <div class="status-center">
                        <i class="fas fa-signal" style="font-size: 10px;"></i>
                        <i class="fas fa-wifi" style="font-size: 10px; margin-left: 4px;"></i>
                    </div>
                    <div class="status-right">
                        <i class="fas fa-battery-three-quarters" style="font-size: 12px;"></i>
                        <span style="font-size: 11px; margin-left: 2px;">85%</span>
                    </div>
                </div>

                <!-- 导航栏 -->
                <div class="nav-bar">
                    <button class="nav-button" data-action="cancel">
                        取消
                    </button>
                    <div class="nav-title">新建记录</div>
                    <button class="nav-button primary" data-action="save">
                        保存
                    </button>
                </div>

                <!-- 主内容 -->
                <div class="main-content">
                    <div class="form-container">
                        <!-- 基本信息 -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-chess section-icon"></i>
                                基本信息
                            </div>
                            
                            <!-- 游戏类型 -->
                            <div class="form-group">
                                <label class="form-label">游戏类型</label>
                                <div class="game-type-grid">
                                    <button class="game-type-btn selected" data-type="象棋">象棋</button>
                                    <button class="game-type-btn" data-type="围棋">围棋</button>
                                    <button class="game-type-btn" data-type="国际象棋">国际象棋</button>
                                    <button class="game-type-btn" data-type="扑克">扑克</button>
                                    <button class="game-type-btn" data-type="麻将">麻将</button>
                                    <button class="game-type-btn" data-type="其他">其他</button>
                                </div>
                            </div>

                            <!-- 对手和结果 -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">对手</label>
                                    <input type="text" class="form-input" placeholder="输入对手姓名" id="opponent-input">
                                </div>
                            </div>

                            <!-- 对局结果 -->
                            <div class="form-group">
                                <label class="form-label">对局结果</label>
                                <div class="result-options">
                                    <button class="result-btn win" data-result="win">
                                        <i class="fas fa-trophy"></i>
                                        胜
                                    </button>
                                    <button class="result-btn loss" data-result="loss">
                                        <i class="fas fa-times"></i>
                                        负
                                    </button>
                                    <button class="result-btn draw" data-result="draw">
                                        <i class="fas fa-handshake"></i>
                                        和
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 时间和地点 -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-clock section-icon"></i>
                                时间地点
                            </div>

                            <!-- 对局时间 -->
                            <div class="form-group">
                                <label class="form-label">对局时间</label>
                                <div class="datetime-input">
                                    <input type="date" class="form-input" id="game-date">
                                    <input type="time" class="form-input" id="game-time">
                                </div>
                            </div>

                            <!-- 用时和地点 -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">用时</label>
                                    <input type="text" class="form-input" placeholder="如：45分钟" id="duration-input">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">地点</label>
                                    <input type="text" class="form-input" placeholder="对局地点" id="location-input">
                                </div>
                            </div>

                            <!-- 快速填充 -->
                            <div class="quick-fill">
                                <button class="quick-fill-btn" data-duration="30分钟">30分钟</button>
                                <button class="quick-fill-btn" data-duration="1小时">1小时</button>
                                <button class="quick-fill-btn" data-duration="2小时">2小时</button>
                                <button class="quick-fill-btn" data-location="家中">家中</button>
                                <button class="quick-fill-btn" data-location="棋牌室">棋牌室</button>
                            </div>
                        </div>

                        <!-- 详细信息 -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-edit section-icon"></i>
                                详细信息
                            </div>

                            <!-- 重要程度 -->
                            <div class="form-group">
                                <label class="form-label">重要程度</label>
                                <div class="slider-container">
                                    <input type="range" min="1" max="5" value="3" class="slider" id="importance-slider">
                                    <div class="slider-labels">
                                        <span>一般</span>
                                        <span>重要</span>
                                        <span>非常重要</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 标签 -->
                            <div class="form-group">
                                <label class="form-label">标签</label>
                                <div class="tag-input-container">
                                    <input type="text" class="form-input" placeholder="添加标签..." id="tag-input">
                                    <div class="tag-suggestions" id="tag-suggestions">
                                        <div class="tag-suggestion">重要对局</div>
                                        <div class="tag-suggestion">练习赛</div>
                                        <div class="tag-suggestion">比赛</div>
                                        <div class="tag-suggestion">在线对局</div>
                                        <div class="tag-suggestion">线下对局</div>
                                    </div>
                                </div>
                                <div class="selected-tags" id="selected-tags">
                                    <!-- 动态添加标签 -->
                                </div>
                            </div>

                            <!-- 对局笔记 -->
                            <div class="form-group">
                                <label class="form-label">对局笔记</label>
                                <textarea class="form-input form-textarea" placeholder="记录对局过程、心得体会、关键步骤等..." id="notes-input"></textarea>
                            </div>

                            <!-- 棋谱记录 -->
                            <div class="form-group">
                                <label class="form-label">棋谱记录</label>
                                <textarea class="form-input form-textarea" placeholder="记录关键步骤和局面..." id="moves-input"></textarea>
                            </div>
                        </div>

                        <!-- 图片附件 -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-image section-icon"></i>
                                图片附件
                            </div>

                            <div class="photo-upload" id="photo-upload">
                                <div class="photo-upload-icon">
                                    <i class="fas fa-camera"></i>
                                </div>
                                <div class="photo-upload-text">点击添加图片</div>
                            </div>

                            <div class="uploaded-photos" id="uploaded-photos">
                                <!-- 动态添加上传的图片 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 保存按钮 -->
                <div class="save-buttons">
                    <button class="btn btn-cancel" data-action="cancel">取消</button>
                    <button class="btn btn-save" data-action="save">保存记录</button>
                </div>

                <!-- Home Indicator -->
                <div class="home-indicator"></div>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script>
        // 新建记录页面特定功能
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认时间
            const now = new Date();
            document.getElementById('game-date').value = now.toISOString().split('T')[0];
            document.getElementById('game-time').value = now.toTimeString().slice(0, 5);

            // 游戏类型选择
            document.querySelectorAll('.game-type-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.game-type-btn').forEach(b => b.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });

            // 结果选择
            document.querySelectorAll('.result-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.result-btn').forEach(b => b.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });

            // 快速填充
            document.querySelectorAll('.quick-fill-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const duration = this.dataset.duration;
                    const location = this.dataset.location;
                    
                    if (duration) {
                        document.getElementById('duration-input').value = duration;
                    }
                    if (location) {
                        document.getElementById('location-input').value = location;
                    }
                });
            });

            // 标签功能
            const tagInput = document.getElementById('tag-input');
            const tagSuggestions = document.getElementById('tag-suggestions');
            const selectedTags = document.getElementById('selected-tags');
            let currentTags = [];

            tagInput.addEventListener('focus', function() {
                tagSuggestions.style.display = 'block';
            });

            tagInput.addEventListener('blur', function() {
                setTimeout(() => {
                    tagSuggestions.style.display = 'none';
                }, 200);
            });

            document.querySelectorAll('.tag-suggestion').forEach(suggestion => {
                suggestion.addEventListener('click', function() {
                    const tag = this.textContent;
                    if (!currentTags.includes(tag)) {
                        addTag(tag);
                    }
                    tagInput.value = '';
                    tagSuggestions.style.display = 'none';
                });
            });

            function addTag(tag) {
                currentTags.push(tag);
                const tagElement = document.createElement('div');
                tagElement.className = 'tag-chip';
                tagElement.innerHTML = `
                    ${tag}
                    <span class="tag-remove" onclick="removeTag('${tag}', this)">×</span>
                `;
                selectedTags.appendChild(tagElement);
            }

            window.removeTag = function(tag, element) {
                currentTags = currentTags.filter(t => t !== tag);
                element.parentElement.remove();
            };

            // 图片上传
            document.getElementById('photo-upload').addEventListener('click', function() {
                // 模拟图片上传
                console.log('打开图片选择器');
            });
        });
    </script>
</body>
</html>
