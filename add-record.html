<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增对局记录</title>
    <link rel="stylesheet" href="ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .form-section {
            background: white;
            margin: var(--spacing-md);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
        }

        .form-group {
            padding: var(--spacing-md);
            border-bottom: 0.5px solid var(--ios-gray5);
        }

        .form-group:last-child {
            border-bottom: none;
        }

        .form-label {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            display: block;
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            border: 1px solid var(--ios-gray4);
            border-radius: var(--radius-md);
            font-size: 17px;
            background: var(--ios-gray6);
            transition: all 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--ios-blue);
            background: white;
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
            font-family: inherit;
        }

        .game-type-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
        }

        .game-type-option {
            padding: var(--spacing-md);
            border: 2px solid var(--ios-gray4);
            border-radius: var(--radius-md);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }

        .game-type-option.selected {
            border-color: var(--ios-blue);
            background: var(--ios-blue);
            color: white;
        }

        .game-type-icon {
            font-size: 24px;
            margin-bottom: var(--spacing-xs);
        }

        .game-type-name {
            font-size: 14px;
            font-weight: 500;
        }

        .result-options {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
        }

        .result-option {
            flex: 1;
            padding: var(--spacing-md);
            border: 2px solid var(--ios-gray4);
            border-radius: var(--radius-md);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }

        .result-option.win.selected {
            border-color: var(--ios-green);
            background: var(--ios-green);
            color: white;
        }

        .result-option.lose.selected {
            border-color: var(--ios-red);
            background: var(--ios-red);
            color: white;
        }

        .result-option.draw.selected {
            border-color: var(--ios-orange);
            background: var(--ios-orange);
            color: white;
        }

        .importance-stars {
            display: flex;
            gap: var(--spacing-xs);
            margin-top: var(--spacing-sm);
        }

        .star {
            font-size: 24px;
            color: var(--ios-gray4);
            cursor: pointer;
            transition: color 0.2s;
        }

        .star.active {
            color: var(--secondary-color);
        }

        .save-button {
            margin: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
            width: calc(100% - 32px);
            padding: var(--spacing-md);
            background: var(--ios-blue);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .save-button:hover {
            background: #0056CC;
        }

        .save-button:disabled {
            background: var(--ios-gray);
            cursor: not-allowed;
        }

        .datetime-input {
            display: flex;
            gap: var(--spacing-sm);
        }

        .datetime-input input {
            flex: 1;
        }

        .tag-input-container {
            position: relative;
        }

        .tag-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid var(--ios-gray4);
            border-top: none;
            border-radius: 0 0 var(--radius-md) var(--radius-md);
            max-height: 150px;
            overflow-y: auto;
            z-index: 10;
            display: none;
        }

        .tag-suggestion {
            padding: var(--spacing-sm) var(--spacing-md);
            cursor: pointer;
            border-bottom: 0.5px solid var(--ios-gray5);
        }

        .tag-suggestion:hover {
            background: var(--ios-gray6);
        }

        .selected-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
            margin-top: var(--spacing-sm);
        }

        .tag-chip {
            background: var(--ios-blue);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .tag-chip .remove {
            cursor: pointer;
            font-size: 10px;
        }

        .nav-buttons {
            display: flex;
            gap: var(--spacing-sm);
        }

        .nav-button.secondary {
            background: transparent;
            color: var(--ios-blue);
            border: 1px solid var(--ios-blue);
            padding: 6px 8px;
            border-radius: 6px;
        }

        .nav-button.secondary:hover {
            background: var(--ios-blue);
            color: white;
        }

        .form-input.error {
            border-color: var(--ios-red);
            background: #fff5f5;
        }

        .form-group.error .form-label {
            color: var(--ios-red);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                    </div>
                    <div class="wifi-icon"></div>
                    <span>100%</span>
                    <div class="battery-icon"></div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <button class="nav-button" onclick="Navigator.back()">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">新增记录</div>
                <div class="nav-buttons">
                    <button class="nav-button" onclick="saveRecord()" id="navSaveButton">保存</button>
                </div>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <form id="recordForm">
                    <!-- 游戏类型 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">游戏类型</label>
                            <div class="game-type-grid" id="gameTypeGrid">
                                <!-- 动态生成游戏类型选项 -->
                            </div>
                        </div>
                    </div>

                    <!-- 基本信息 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">对手姓名</label>
                            <input type="text" class="form-input" id="opponent" placeholder="请输入对手姓名" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">对局时间</label>
                            <div class="datetime-input">
                                <input type="date" class="form-input" id="gameDate" required>
                                <input type="time" class="form-input" id="gameTime" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">对局地点</label>
                            <input type="text" class="form-input" id="location" placeholder="请输入对局地点">
                        </div>
                    </div>

                    <!-- 对局结果 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">对局结果</label>
                            <div class="result-options">
                                <div class="result-option win" data-result="胜">
                                    <div style="font-size: 20px; margin-bottom: 4px;">🏆</div>
                                    <div>胜利</div>
                                </div>
                                <div class="result-option lose" data-result="负">
                                    <div style="font-size: 20px; margin-bottom: 4px;">😔</div>
                                    <div>失败</div>
                                </div>
                                <div class="result-option draw" data-result="和">
                                    <div style="font-size: 20px; margin-bottom: 4px;">🤝</div>
                                    <div>平局</div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">对局用时</label>
                            <input type="text" class="form-input" id="duration" placeholder="例如：1小时30分钟">
                        </div>
                    </div>

                    <!-- 重要程度 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">重要程度</label>
                            <div class="importance-stars" id="importanceStars">
                                <span class="star" data-rating="1">⭐</span>
                                <span class="star" data-rating="2">⭐</span>
                                <span class="star" data-rating="3">⭐</span>
                                <span class="star" data-rating="4">⭐</span>
                                <span class="star" data-rating="5">⭐</span>
                            </div>
                        </div>
                    </div>

                    <!-- 对局笔记 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">对局笔记</label>
                            <textarea class="form-input form-textarea" id="notes" placeholder="记录对局中的思考过程、关键步骤、心得体会等..."></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">棋谱记录</label>
                            <textarea class="form-input form-textarea" id="moves" placeholder="记录关键步骤和局面变化..."></textarea>
                        </div>
                    </div>

                    <!-- 标签 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">标签</label>
                            <div class="tag-input-container">
                                <input type="text" class="form-input" id="tagInput" placeholder="添加标签，按回车确认">
                                <div class="tag-suggestions" id="tagSuggestions"></div>
                            </div>
                            <div class="selected-tags" id="selectedTags"></div>
                        </div>
                    </div>
                </form>

                <button class="save-button" onclick="saveRecord()">
                    <i class="fas fa-save"></i> 保存记录
                </button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="app-utils.js"></script>
    <script>
        let selectedGameType = '';
        let selectedResult = '';
        let selectedImportance = 1;
        let selectedTags = [];
        let draftTimer = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initGameTypes();
            initResultOptions();
            initImportanceStars();
            initDateTime();
            initTagInput();
            initFormValidation();
            initDraftSaving();

            // 检查是否为编辑模式或预填充模式
            const params = Navigator.getParams();
            if (params.edit === 'true') {
                document.querySelector('.nav-title').textContent = '编辑记录';
                loadEditData(params);
            } else {
                // 检查是否有预填充的对手信息
                if (params.opponent) {
                    document.getElementById('opponent').value = params.opponent;
                    UIUtils.showToast('已预填充对手信息', 'info');
                }

                // 新增模式，尝试加载草稿
                loadDraft();
            }
        });

        // 初始化表单验证
        function initFormValidation() {
            // 实时验证对手姓名
            const opponentInput = document.getElementById('opponent');
            opponentInput.addEventListener('input', function() {
                validateField(this, this.value.trim().length > 0 && this.value.trim().length <= 50);
            });

            // 实时验证地点
            const locationInput = document.getElementById('location');
            locationInput.addEventListener('input', function() {
                validateField(this, this.value.trim().length <= 100);
            });

            // 实时验证用时
            const durationInput = document.getElementById('duration');
            durationInput.addEventListener('input', function() {
                validateField(this, this.value.trim().length <= 20);
            });

            // 实时验证笔记
            const notesInput = document.getElementById('notes');
            notesInput.addEventListener('input', function() {
                validateField(this, this.value.trim().length <= 1000);
                updateCharCount('notes', this.value.length, 1000);
            });

            // 实时验证棋谱
            const movesInput = document.getElementById('moves');
            movesInput.addEventListener('input', function() {
                validateField(this, this.value.trim().length <= 2000);
                updateCharCount('moves', this.value.length, 2000);
            });
        }

        // 验证单个字段
        function validateField(input, isValid) {
            const formGroup = input.closest('.form-group');
            if (isValid) {
                input.classList.remove('error');
                formGroup.classList.remove('error');
            } else {
                input.classList.add('error');
                formGroup.classList.add('error');
            }
        }

        // 更新字符计数
        function updateCharCount(fieldId, current, max) {
            let counter = document.getElementById(fieldId + 'Counter');
            if (!counter) {
                counter = document.createElement('div');
                counter.id = fieldId + 'Counter';
                counter.style.cssText = 'font-size: 12px; color: var(--ios-gray); text-align: right; margin-top: 4px;';
                document.getElementById(fieldId).parentNode.appendChild(counter);
            }
            counter.textContent = `${current}/${max}`;
            counter.style.color = current > max ? 'var(--ios-red)' : 'var(--ios-gray)';
        }

        // 键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + S 保存
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                saveRecord();
            }



            // ESC 返回
            if (e.key === 'Escape') {
                Navigator.back();
            }
        });

        // 初始化草稿保存
        function initDraftSaving() {
            // 监听表单变化，自动保存草稿
            const formInputs = document.querySelectorAll('#recordForm input, #recordForm textarea');
            formInputs.forEach(input => {
                input.addEventListener('input', scheduleDraftSave);
            });
        }

        // 计划保存草稿
        function scheduleDraftSave() {
            if (draftTimer) {
                clearTimeout(draftTimer);
            }
            draftTimer = setTimeout(saveDraft, 2000); // 2秒后保存
        }

        // 保存草稿
        function saveDraft() {
            const params = Navigator.getParams();
            if (params.edit === 'true') return; // 编辑模式不保存草稿

            const draft = {
                gameType: selectedGameType,
                opponent: document.getElementById('opponent').value.trim(),
                gameDate: document.getElementById('gameDate').value,
                gameTime: document.getElementById('gameTime').value,
                location: document.getElementById('location').value.trim(),
                result: selectedResult,
                duration: document.getElementById('duration').value.trim(),
                importance: selectedImportance,
                notes: document.getElementById('notes').value.trim(),
                moves: document.getElementById('moves').value.trim(),
                tags: [...selectedTags],
                timestamp: Date.now()
            };

            // 只有当表单有内容时才保存草稿
            const hasContent = draft.gameType || draft.opponent || draft.notes || draft.moves ||
                              draft.location || draft.duration || draft.tags.length > 0;

            if (hasContent) {
                localStorage.setItem('recordDraft', JSON.stringify(draft));
            }
        }

        // 加载草稿
        function loadDraft() {
            const draftStr = localStorage.getItem('recordDraft');
            if (!draftStr) return;

            try {
                const draft = JSON.parse(draftStr);

                // 检查草稿是否过期（24小时）
                if (Date.now() - draft.timestamp > 24 * 60 * 60 * 1000) {
                    localStorage.removeItem('recordDraft');
                    return;
                }

                // 询问是否恢复草稿
                UIUtils.showDialog('发现草稿', '检测到未完成的记录草稿，是否恢复？', [
                    {
                        text: '忽略',
                        action: () => {
                            localStorage.removeItem('recordDraft');
                        }
                    },
                    {
                        text: '恢复',
                        action: () => {
                            restoreDraft(draft);
                            localStorage.removeItem('recordDraft');
                        }
                    }
                ]);
            } catch (error) {
                console.error('加载草稿失败:', error);
                localStorage.removeItem('recordDraft');
            }
        }

        // 恢复草稿
        function restoreDraft(draft) {
            selectedGameType = draft.gameType || '';
            selectedResult = draft.result || '';
            selectedImportance = draft.importance || 1;
            selectedTags = draft.tags || [];

            // 恢复游戏类型选择
            if (draft.gameType) {
                document.querySelectorAll('.game-type-option').forEach(option => {
                    option.classList.remove('selected');
                    if (option.dataset.type === draft.gameType) {
                        option.classList.add('selected');
                    }
                });
            }

            // 恢复结果选择
            if (draft.result) {
                document.querySelectorAll('.result-option').forEach(option => {
                    option.classList.remove('selected');
                    if (option.dataset.result === draft.result) {
                        option.classList.add('selected');
                    }
                });
            }

            // 恢复表单字段
            document.getElementById('opponent').value = draft.opponent || '';
            document.getElementById('gameDate').value = draft.gameDate || '';
            document.getElementById('gameTime').value = draft.gameTime || '';
            document.getElementById('location').value = draft.location || '';
            document.getElementById('duration').value = draft.duration || '';
            document.getElementById('notes').value = draft.notes || '';
            document.getElementById('moves').value = draft.moves || '';

            // 更新显示
            updateImportanceStars();
            updateTagsDisplay();

            UIUtils.showToast('草稿已恢复', 'success');
        }

        // 清除草稿
        function clearDraft() {
            localStorage.removeItem('recordDraft');
        }

        // 加载编辑数据
        function loadEditData(params) {
            // 如果有记录ID，从存储中加载完整数据
            if (params.id) {
                const records = appStorage.get('gameRecords') || [];
                const record = records.find(r => r.id === params.id);
                if (record) {
                    loadRecordData(record);
                    return;
                }
            }

            // 从URL参数加载数据（兼容旧方式）
            if (params.gameType) {
                selectedGameType = params.gameType;
                document.querySelectorAll('.game-type-option').forEach(option => {
                    option.classList.remove('selected');
                    if (option.dataset.type === params.gameType) {
                        option.classList.add('selected');
                    }
                });
            }
            if (params.opponent) {
                document.getElementById('opponent').value = decodeURIComponent(params.opponent);
            }
            if (params.result) {
                selectedResult = params.result;
                document.querySelectorAll('.result-option').forEach(option => {
                    option.classList.remove('selected');
                    if (option.dataset.result === params.result) {
                        option.classList.add('selected');
                    }
                });
            }
            if (params.importance) {
                selectedImportance = parseInt(params.importance);
                updateImportanceStars();
            }
            if (params.notes) {
                document.getElementById('notes').value = decodeURIComponent(params.notes);
            }
            if (params.moves) {
                document.getElementById('moves').value = decodeURIComponent(params.moves);
            }
            if (params.location) {
                document.getElementById('location').value = decodeURIComponent(params.location);
            }
            if (params.duration) {
                document.getElementById('duration').value = decodeURIComponent(params.duration);
            }
            if (params.date) {
                const date = new Date(params.date);
                document.getElementById('gameDate').value = date.toISOString().split('T')[0];
                document.getElementById('gameTime').value = date.toTimeString().slice(0, 5);
            }
            if (params.tags) {
                const tags = params.tags.split(',').filter(tag => tag.trim());
                selectedTags = tags;
                updateTagsDisplay();
            }
        }

        // 从记录对象加载数据
        function loadRecordData(record) {
            selectedGameType = record.gameType;
            selectedResult = record.result;
            selectedImportance = record.importance || 1;
            selectedTags = record.tags || [];

            // 更新游戏类型选择
            document.querySelectorAll('.game-type-option').forEach(option => {
                option.classList.remove('selected');
                if (option.dataset.type === record.gameType) {
                    option.classList.add('selected');
                }
            });

            // 更新结果选择
            document.querySelectorAll('.result-option').forEach(option => {
                option.classList.remove('selected');
                if (option.dataset.result === record.result) {
                    option.classList.add('selected');
                }
            });

            // 填充表单字段
            document.getElementById('opponent').value = record.opponent || '';
            document.getElementById('location').value = record.location || '';
            document.getElementById('duration').value = record.duration || '';
            document.getElementById('notes').value = record.notes || '';
            document.getElementById('moves').value = record.moves || '';

            // 设置日期时间
            if (record.date) {
                const date = new Date(record.date);
                document.getElementById('gameDate').value = date.toISOString().split('T')[0];
                document.getElementById('gameTime').value = date.toTimeString().slice(0, 5);
            }

            // 更新重要程度和标签显示
            updateImportanceStars();
            updateTagsDisplay();
        }

        // 初始化游戏类型
        function initGameTypes() {
            const grid = document.getElementById('gameTypeGrid');
            grid.innerHTML = Object.keys(GAME_TYPES).map(type => `
                <div class="game-type-option" data-type="${type}">
                    <div class="game-type-icon">${GAME_TYPES[type].icon}</div>
                    <div class="game-type-name">${type}</div>
                </div>
            `).join('');

            // 添加点击事件
            grid.addEventListener('click', function(e) {
                const option = e.target.closest('.game-type-option');
                if (option) {
                    document.querySelectorAll('.game-type-option').forEach(opt => opt.classList.remove('selected'));
                    option.classList.add('selected');
                    selectedGameType = option.dataset.type;
                }
            });
        }

        // 初始化结果选项
        function initResultOptions() {
            document.querySelectorAll('.result-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.result-option').forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedResult = this.dataset.result;
                });
            });
        }

        // 初始化重要程度星级
        function initImportanceStars() {
            const stars = document.querySelectorAll('.star');
            stars.forEach((star, index) => {
                star.addEventListener('click', function() {
                    selectedImportance = parseInt(this.dataset.rating);
                    updateImportanceStars();
                });
            });

            updateImportanceStars();
        }

        // 更新重要程度星级显示
        function updateImportanceStars() {
            const stars = document.querySelectorAll('.star');
            stars.forEach((star, index) => {
                star.classList.toggle('active', index < selectedImportance);
            });
        }

        // 初始化日期时间
        function initDateTime() {
            const now = new Date();
            document.getElementById('gameDate').value = now.toISOString().split('T')[0];
            document.getElementById('gameTime').value = now.toTimeString().slice(0, 5);
        }

        // 初始化标签输入
        function initTagInput() {
            const tagInput = document.getElementById('tagInput');
            const suggestions = document.getElementById('tagSuggestions');

            tagInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    addTag(this.value.trim());
                    this.value = '';
                    suggestions.style.display = 'none';
                }
            });

            tagInput.addEventListener('input', function() {
                const value = this.value.trim();
                if (value.length > 0) {
                    showTagSuggestions(value);
                } else {
                    suggestions.style.display = 'none';
                }
            });

            tagInput.addEventListener('focus', function() {
                if (this.value.trim().length > 0) {
                    showTagSuggestions(this.value.trim());
                }
            });

            // 点击外部隐藏建议
            document.addEventListener('click', function(e) {
                if (!tagInput.contains(e.target) && !suggestions.contains(e.target)) {
                    suggestions.style.display = 'none';
                }
            });
        }

        // 显示标签建议
        function showTagSuggestions(input) {
            const suggestions = document.getElementById('tagSuggestions');
            const allTags = getAllExistingTags();

            // 过滤匹配的标签（排除已选择的）
            const matchedTags = allTags.filter(tag =>
                tag.toLowerCase().includes(input.toLowerCase()) &&
                !selectedTags.includes(tag)
            );

            if (matchedTags.length > 0) {
                suggestions.innerHTML = matchedTags.map(tag =>
                    `<div class="tag-suggestion" onclick="selectSuggestion('${tag}')">${tag}</div>`
                ).join('');
                suggestions.style.display = 'block';
            } else {
                suggestions.style.display = 'none';
            }
        }

        // 获取所有已存在的标签
        function getAllExistingTags() {
            const records = appStorage.get('gameRecords') || [];
            const tagSet = new Set();

            records.forEach(record => {
                if (record.tags && Array.isArray(record.tags)) {
                    record.tags.forEach(tag => tagSet.add(tag));
                }
            });

            // 添加一些常用标签建议
            const commonTags = ['重要对局', '练习', '比赛', '友谊赛', '线上', '线下', '快棋', '慢棋', '复盘', '学习'];
            commonTags.forEach(tag => tagSet.add(tag));

            return Array.from(tagSet).sort();
        }

        // 选择建议标签
        function selectSuggestion(tag) {
            addTag(tag);
            document.getElementById('tagInput').value = '';
            document.getElementById('tagSuggestions').style.display = 'none';
        }

        // 添加标签
        function addTag(tagName) {
            if (tagName && !selectedTags.includes(tagName)) {
                selectedTags.push(tagName);
                updateTagsDisplay();
            }
        }

        // 移除标签
        function removeTag(tagName) {
            selectedTags = selectedTags.filter(tag => tag !== tagName);
            updateTagsDisplay();
        }

        // 更新标签显示
        function updateTagsDisplay() {
            const container = document.getElementById('selectedTags');
            container.innerHTML = selectedTags.map(tag => `
                <div class="tag-chip">
                    ${tag}
                    <span class="remove" onclick="removeTag('${tag}')">×</span>
                </div>
            `).join('');
        }

        // 表单验证
        function validateForm() {
            const errors = [];

            if (!selectedGameType) {
                errors.push('请选择游戏类型');
            }

            const opponent = document.getElementById('opponent').value.trim();
            if (!opponent) {
                errors.push('请输入对手姓名');
            } else if (opponent.length > 50) {
                errors.push('对手姓名不能超过50个字符');
            }

            if (!selectedResult) {
                errors.push('请选择对局结果');
            }

            const gameDate = document.getElementById('gameDate').value;
            const gameTime = document.getElementById('gameTime').value;
            if (!gameDate || !gameTime) {
                errors.push('请选择对局时间');
            } else {
                const gameDateTime = new Date(gameDate + 'T' + gameTime);
                const now = new Date();
                if (gameDateTime > now) {
                    errors.push('对局时间不能是未来时间');
                }
            }

            const duration = document.getElementById('duration').value.trim();
            if (duration && duration.length > 20) {
                errors.push('对局用时描述不能超过20个字符');
            }

            const location = document.getElementById('location').value.trim();
            if (location && location.length > 100) {
                errors.push('对局地点不能超过100个字符');
            }

            const notes = document.getElementById('notes').value.trim();
            if (notes && notes.length > 1000) {
                errors.push('对局笔记不能超过1000个字符');
            }

            const moves = document.getElementById('moves').value.trim();
            if (moves && moves.length > 2000) {
                errors.push('棋谱记录不能超过2000个字符');
            }

            if (selectedTags.length > 10) {
                errors.push('标签数量不能超过10个');
            }

            return errors;
        }

        // 保存记录
        function saveRecord() {
            console.log('saveRecord function called');
            console.log('Current form state:');
            console.log('- selectedGameType:', selectedGameType);
            console.log('- selectedResult:', selectedResult);
            console.log('- opponent:', document.getElementById('opponent').value);
            console.log('- gameDate:', document.getElementById('gameDate').value);
            console.log('- gameTime:', document.getElementById('gameTime').value);

            // 验证表单
            const errors = validateForm();
            if (errors.length > 0) {
                console.log('Validation errors:', errors);
                UIUtils.showToast(errors[0], 'error');
                return;
            }

            console.log('Form validation passed');

            // 禁用保存按钮防止重复提交
            const saveButton = document.querySelector('.save-button');
            const navSaveButton = document.getElementById('navSaveButton');
            saveButton.disabled = true;
            if (navSaveButton) navSaveButton.disabled = true;

            const params = Navigator.getParams();
            const isEdit = params.edit === 'true';
            console.log('Save mode:', isEdit ? 'Edit' : 'New', 'Params:', params);

            // 构建记录对象
            const record = {
                gameType: selectedGameType,
                opponent: document.getElementById('opponent').value.trim(),
                date: document.getElementById('gameDate').value + 'T' + document.getElementById('gameTime').value,
                location: document.getElementById('location').value.trim(),
                result: selectedResult,
                duration: document.getElementById('duration').value.trim(),
                importance: selectedImportance,
                notes: document.getElementById('notes').value.trim(),
                moves: document.getElementById('moves').value.trim(),
                tags: [...selectedTags], // 创建副本
                gameNature: '普通对局' // 默认值
            };

            try {
                if (isEdit && params.id) {
                    // 编辑模式：更新现有记录
                    record.id = params.id;
                    const updatedRecord = appStorage.updateRecord(record);
                    if (updatedRecord) {
                        UIUtils.showToast('记录更新成功', 'success');
                        console.log('Record updated successfully, navigating to detail page...');
                        // 延迟跳转
                        setTimeout(() => {
                            Navigator.navigate('record-detail.html', { id: params.id });
                        }, 1000);
                    } else {
                        throw new Error('更新记录失败');
                    }
                } else {
                    // 新增模式：添加新记录
                    const newRecord = appStorage.addRecord(record);
                    if (newRecord) {
                        clearDraft(); // 清除草稿
                        UIUtils.showToast('记录保存成功', 'success');
                        console.log('Record saved successfully, navigating to records page...');
                        // 延迟跳转
                        setTimeout(() => {
                            Navigator.navigate('records.html');
                        }, 1000);
                    } else {
                        throw new Error('保存记录失败');
                    }
                }
            } catch (error) {
                console.error('保存记录失败:', error);
                UIUtils.showToast('保存失败，请重试', 'error');
                // 重新启用按钮
                saveButton.disabled = false;
                if (navSaveButton) navSaveButton.disabled = false;
            }
        }




    </script>
</body>
</html>
