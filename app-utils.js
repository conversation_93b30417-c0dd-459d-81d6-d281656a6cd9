// 棋牌记事本App工具库

// 模拟数据存储
class AppStorage {
  constructor() {
    this.data = {
      gameRecords: [],
      opponents: [],
      knowledge: [],
      tags: [],
      statistics: {},
      settings: {
        theme: 'light',
        defaultGameType: '象棋',
        notifications: true
      }
    };
    this.loadData();
  }

  // 从localStorage加载数据
  loadData() {
    const stored = localStorage.getItem('chessCardNotebook');
    if (stored) {
      this.data = { ...this.data, ...JSON.parse(stored) };
    }
  }

  // 保存数据到localStorage
  saveData() {
    localStorage.setItem('chessCardNotebook', JSON.stringify(this.data));
  }

  // 获取数据
  get(key) {
    return this.data[key];
  }

  // 设置数据
  set(key, value) {
    this.data[key] = value;
    this.saveData();
  }

  // 添加记录
  addRecord(record) {
    record.id = Date.now().toString();
    record.createdAt = new Date().toISOString();
    record.updatedAt = record.createdAt;
    this.data.gameRecords.unshift(record);
    this.saveData();
    this.updateStatistics();
    return record;
  }

  // 更新记录
  updateRecord(updatedRecord) {
    const records = this.data.gameRecords;
    const index = records.findIndex(record => record.id === updatedRecord.id);

    if (index !== -1) {
      // 保留原有的创建时间，更新修改时间
      updatedRecord.createdAt = records[index].createdAt;
      updatedRecord.updatedAt = new Date().toISOString();
      records[index] = updatedRecord;
      this.saveData();
      this.updateStatistics();
      return updatedRecord;
    }
    return null;
  }

  // 删除记录
  deleteRecord(recordId) {
    const records = this.data.gameRecords;
    const index = records.findIndex(record => record.id === recordId);

    if (index !== -1) {
      const deletedRecord = records.splice(index, 1)[0];
      this.saveData();
      this.updateStatistics();
      this.updateOpponentsFromRecords();
      return deletedRecord;
    }
    return null;
  }

  // 批量删除记录
  deleteRecords(recordIds) {
    const records = this.data.gameRecords;
    const deletedRecords = [];

    recordIds.forEach(id => {
      const index = records.findIndex(record => record.id === id);
      if (index !== -1) {
        deletedRecords.push(records.splice(index, 1)[0]);
      }
    });

    if (deletedRecords.length > 0) {
      this.saveData();
      this.updateStatistics();
      this.updateOpponentsFromRecords();
    }

    return deletedRecords;
  }

  // 搜索记录
  searchRecords(query, filters = {}) {
    const records = this.data.gameRecords;
    let filteredRecords = [...records];

    // 文本搜索
    if (query && query.trim()) {
      const searchTerm = query.toLowerCase().trim();
      filteredRecords = filteredRecords.filter(record =>
        record.opponent.toLowerCase().includes(searchTerm) ||
        record.gameType.toLowerCase().includes(searchTerm) ||
        (record.notes && record.notes.toLowerCase().includes(searchTerm)) ||
        (record.location && record.location.toLowerCase().includes(searchTerm)) ||
        (record.tags && record.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
      );
    }

    // 游戏类型筛选
    if (filters.gameType && filters.gameType !== 'all') {
      filteredRecords = filteredRecords.filter(record => record.gameType === filters.gameType);
    }

    // 结果筛选
    if (filters.result && filters.result !== 'all') {
      filteredRecords = filteredRecords.filter(record => record.result === filters.result);
    }

    // 对手筛选
    if (filters.opponent) {
      filteredRecords = filteredRecords.filter(record =>
        record.opponent.toLowerCase().includes(filters.opponent.toLowerCase())
      );
    }

    // 日期范围筛选
    if (filters.dateFrom) {
      filteredRecords = filteredRecords.filter(record =>
        new Date(record.date) >= new Date(filters.dateFrom)
      );
    }
    if (filters.dateTo) {
      filteredRecords = filteredRecords.filter(record =>
        new Date(record.date) <= new Date(filters.dateTo)
      );
    }

    return filteredRecords;
  }

  // 更新统计数据
  updateStatistics() {
    const records = this.data.gameRecords;
    const stats = {
      overall: {
        totalGames: records.length,
        wins: 0,
        losses: 0,
        draws: 0,
        winRate: 0
      },
      byGameType: {},
      byOpponent: {},
      byMonth: {},
      recentTrend: []
    };

    // 计算总体统计
    records.forEach(record => {
      if (record.result === '胜') stats.overall.wins++;
      else if (record.result === '负') stats.overall.losses++;
      else if (record.result === '和') stats.overall.draws++;
    });

    stats.overall.winRate = stats.overall.totalGames > 0 ?
      (stats.overall.wins / stats.overall.totalGames * 100).toFixed(1) : 0;

    // 按游戏类型统计
    records.forEach(record => {
      if (!stats.byGameType[record.gameType]) {
        stats.byGameType[record.gameType] = {
          totalGames: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0
        };
      }

      stats.byGameType[record.gameType].totalGames++;
      if (record.result === '胜') stats.byGameType[record.gameType].wins++;
      else if (record.result === '负') stats.byGameType[record.gameType].losses++;
      else if (record.result === '和') stats.byGameType[record.gameType].draws++;
    });

    // 计算各游戏类型胜率
    Object.keys(stats.byGameType).forEach(gameType => {
      const stat = stats.byGameType[gameType];
      stat.winRate = stat.totalGames > 0 ? (stat.wins / stat.totalGames * 100).toFixed(1) : 0;
    });

    // 按对手统计
    records.forEach(record => {
      if (!stats.byOpponent[record.opponent]) {
        stats.byOpponent[record.opponent] = {
          totalGames: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0
        };
      }

      stats.byOpponent[record.opponent].totalGames++;
      if (record.result === '胜') stats.byOpponent[record.opponent].wins++;
      else if (record.result === '负') stats.byOpponent[record.opponent].losses++;
      else if (record.result === '和') stats.byOpponent[record.opponent].draws++;
    });

    // 计算对手胜率
    Object.keys(stats.byOpponent).forEach(opponent => {
      const stat = stats.byOpponent[opponent];
      stat.winRate = stat.totalGames > 0 ? (stat.wins / stat.totalGames * 100).toFixed(1) : 0;
    });

    // 按月份统计（最近12个月）
    const now = new Date();
    for (let i = 11; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      stats.byMonth[monthKey] = {
        totalGames: 0,
        wins: 0,
        losses: 0,
        draws: 0,
        winRate: 0
      };
    }

    records.forEach(record => {
      const recordDate = new Date(record.date);
      const monthKey = `${recordDate.getFullYear()}-${String(recordDate.getMonth() + 1).padStart(2, '0')}`;

      if (stats.byMonth[monthKey]) {
        stats.byMonth[monthKey].totalGames++;
        if (record.result === '胜') stats.byMonth[monthKey].wins++;
        else if (record.result === '负') stats.byMonth[monthKey].losses++;
        else if (record.result === '和') stats.byMonth[monthKey].draws++;
      }
    });

    // 计算月度胜率
    Object.keys(stats.byMonth).forEach(month => {
      const stat = stats.byMonth[month];
      stat.winRate = stat.totalGames > 0 ? (stat.wins / stat.totalGames * 100).toFixed(1) : 0;
    });

    this.data.statistics = stats;
    this.saveData();
  }

  // 从记录更新对手信息
  updateOpponentsFromRecords() {
    const records = this.data.gameRecords;
    const existingOpponents = this.data.opponents || [];
    const opponentMap = new Map();

    // 保留现有对手的详细信息
    existingOpponents.forEach(opponent => {
      opponentMap.set(opponent.name, opponent);
    });

    // 从记录中提取对手信息并更新统计
    const opponentStats = {};
    records.forEach(record => {
      const opponentName = record.opponent;

      if (!opponentStats[opponentName]) {
        opponentStats[opponentName] = {
          totalGames: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          recentGames: []
        };
      }

      opponentStats[opponentName].totalGames++;
      if (record.result === '胜') opponentStats[opponentName].wins++;
      else if (record.result === '负') opponentStats[opponentName].losses++;
      else if (record.result === '和') opponentStats[opponentName].draws++;

      opponentStats[opponentName].recentGames.push({
        id: record.id,
        gameType: record.gameType,
        result: record.result,
        date: record.date
      });
    });

    // 更新或创建对手信息
    Object.keys(opponentStats).forEach(opponentName => {
      const stats = opponentStats[opponentName];
      stats.recentGames.sort((a, b) => new Date(b.date) - new Date(a.date));
      stats.recentGames = stats.recentGames.slice(0, 5); // 只保留最近5局

      if (opponentMap.has(opponentName)) {
        // 更新现有对手的统计信息
        const opponent = opponentMap.get(opponentName);
        Object.assign(opponent, stats);
        opponent.winRate = stats.totalGames > 0 ? (stats.wins / stats.totalGames * 100).toFixed(1) : 0;
      } else {
        // 创建新对手
        const newOpponent = {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          name: opponentName,
          avatar: opponentName.charAt(0).toUpperCase(),
          level: '未知',
          style: '待分析',
          strengths: '',
          weaknesses: '',
          notes: '',
          createdAt: new Date().toISOString(),
          winRate: stats.totalGames > 0 ? (stats.wins / stats.totalGames * 100).toFixed(1) : 0,
          ...stats
        };
        opponentMap.set(opponentName, newOpponent);
      }
    });

    this.data.opponents = Array.from(opponentMap.values());
    this.saveData();
  }

  // 技巧库管理
  addKnowledge(knowledge) {
    knowledge.id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    knowledge.createdAt = new Date().toISOString();
    knowledge.updatedAt = knowledge.createdAt;
    knowledge.views = 0;
    knowledge.favorites = 0;
    knowledge.isFavorited = false;

    this.data.knowledge.unshift(knowledge);
    this.saveData();
    return knowledge;
  }

  updateKnowledge(updatedKnowledge) {
    const knowledge = this.data.knowledge;
    const index = knowledge.findIndex(item => item.id === updatedKnowledge.id);

    if (index !== -1) {
      updatedKnowledge.updatedAt = new Date().toISOString();
      knowledge[index] = { ...knowledge[index], ...updatedKnowledge };
      this.saveData();
      return knowledge[index];
    }
    return null;
  }

  deleteKnowledge(knowledgeId) {
    const knowledge = this.data.knowledge;
    const index = knowledge.findIndex(item => item.id === knowledgeId);

    if (index !== -1) {
      const deletedKnowledge = knowledge.splice(index, 1)[0];
      this.saveData();
      return deletedKnowledge;
    }
    return null;
  }

  searchKnowledge(query, filters = {}) {
    const knowledge = this.data.knowledge;
    let filteredKnowledge = [...knowledge];

    // 文本搜索
    if (query && query.trim()) {
      const searchTerm = query.toLowerCase().trim();
      filteredKnowledge = filteredKnowledge.filter(item =>
        item.title.toLowerCase().includes(searchTerm) ||
        item.content.toLowerCase().includes(searchTerm) ||
        (item.tags && item.tags.some(tag => tag.toLowerCase().includes(searchTerm))) ||
        (item.gameType && item.gameType.toLowerCase().includes(searchTerm))
      );
    }

    // 分类筛选
    if (filters.category && filters.category !== 'all') {
      filteredKnowledge = filteredKnowledge.filter(item => item.category === filters.category);
    }

    // 游戏类型筛选
    if (filters.gameType && filters.gameType !== 'all') {
      filteredKnowledge = filteredKnowledge.filter(item => item.gameType === filters.gameType);
    }

    // 难度筛选
    if (filters.difficulty && filters.difficulty !== 'all') {
      filteredKnowledge = filteredKnowledge.filter(item => item.difficulty === filters.difficulty);
    }

    // 收藏筛选
    if (filters.favorited) {
      filteredKnowledge = filteredKnowledge.filter(item => item.isFavorited);
    }

    return filteredKnowledge;
  }

  toggleKnowledgeFavorite(knowledgeId) {
    const knowledge = this.data.knowledge;
    const item = knowledge.find(k => k.id === knowledgeId);

    if (item) {
      item.isFavorited = !item.isFavorited;
      item.favorites += item.isFavorited ? 1 : -1;
      this.saveData();
      return item;
    }
    return null;
  }

  incrementKnowledgeViews(knowledgeId) {
    const knowledge = this.data.knowledge;
    const item = knowledge.find(k => k.id === knowledgeId);

    if (item) {
      item.views = (item.views || 0) + 1;
      this.saveData();
      return item;
    }
    return null;
  }

  // 初始化示例数据
  initSampleData() {
    if (this.data.gameRecords.length === 0) {
      this.initSampleRecords();
    }
    if (this.data.knowledge.length === 0) {
      this.initSampleKnowledge();
    }
  }

  initSampleRecords() {
    const sampleRecords = [
      {
        gameType: '象棋',
        opponent: '张三',
        date: new Date(Date.now() - 86400000).toISOString(),
        location: '棋牌室',
        result: '胜',
        duration: '45分钟',
        importance: 3,
        notes: '开局采用中炮对屏风马，中局成功突破对方防线。',
        moves: '1.炮二平五 马8进7 2.马二进三 车9平8...',
        tags: ['中炮', '屏风马', '中局突破'],
        gameNature: '普通对局'
      },
      {
        gameType: '围棋',
        opponent: '李四',
        date: new Date(Date.now() - 172800000).toISOString(),
        location: '围棋社',
        result: '负',
        duration: '1小时20分钟',
        importance: 4,
        notes: '布局阶段落后，中盘未能追回差距。需要加强定式学习。',
        moves: '',
        tags: ['布局', '定式', '中盘'],
        gameNature: '普通对局'
      },
      {
        gameType: '象棋',
        opponent: '王五',
        date: new Date(Date.now() - 259200000).toISOString(),
        location: '公园',
        result: '和',
        duration: '30分钟',
        importance: 2,
        notes: '残局阶段双方都有机会，最终和棋。',
        moves: '',
        tags: ['残局', '和棋'],
        gameNature: '普通对局'
      }
    ];

    sampleRecords.forEach(record => this.addRecord(record));
  }

  initSampleKnowledge() {
    const sampleKnowledge = [
      {
        title: '象棋开局：中炮对屏风马',
        content: '中炮对屏风马是象棋中最经典的开局之一。红方先手炮二平五，黑方应以马8进7形成屏风马阵型。这种开局攻守兼备，变化丰富，是初学者必须掌握的基本开局。\n\n要点：\n1. 红方要注意控制中路\n2. 黑方屏风马要配合车炮协调\n3. 双方都要注意子力的协调发展',
        category: '开局',
        gameType: '象棋',
        difficulty: '初级',
        tags: ['中炮', '屏风马', '经典开局'],
        author: '系统'
      },
      {
        title: '围棋基础：星位布局要点',
        content: '星位是围棋布局中最常见的开局点。占据星位可以快速展开，兼顾角部和边部的发展。\n\n星位布局的优势：\n1. 发展速度快\n2. 便于形成大模样\n3. 攻守兼备\n\n注意事项：\n1. 要配合其他子力\n2. 注意角部的安全\n3. 适时转换攻守',
        category: '开局',
        gameType: '围棋',
        difficulty: '初级',
        tags: ['星位', '布局', '模样'],
        author: '系统'
      },
      {
        title: '象棋残局：单车胜单士',
        content: '单车胜单士是象棋残局的基本功。掌握这个残局对提高棋力很重要。\n\n胜法要点：\n1. 车要控制对方将帅的活动\n2. 逐步压缩对方空间\n3. 寻找机会将死对方\n\n常见错误：\n1. 急于求成\n2. 不注意对方的反击\n3. 走法不够精确',
        category: '残局',
        gameType: '象棋',
        difficulty: '中级',
        tags: ['单车', '残局', '基本功'],
        author: '系统'
      },
      {
        title: '心理战术：保持冷静的重要性',
        content: '在棋类对弈中，心理素质往往决定胜负。保持冷静是获胜的关键因素之一。\n\n保持冷静的方法：\n1. 深呼吸，放松心情\n2. 专注于棋局本身\n3. 不被对手的表情影响\n4. 合理分配时间\n\n优势：\n1. 减少失误\n2. 更好的判断力\n3. 稳定的发挥',
        category: '心得',
        gameType: '通用',
        difficulty: '初级',
        tags: ['心理', '冷静', '心得'],
        author: '系统'
      }
    ];

    sampleKnowledge.forEach(knowledge => this.addKnowledge(knowledge));
  }
}

// 全局存储实例
const appStorage = new AppStorage();

// 页面导航工具
class Navigator {
  static navigate(page, params = {}) {
    try {
      // 获取当前页面的基础路径
      const currentPath = window.location.pathname;
      const basePath = currentPath.substring(0, currentPath.lastIndexOf('/') + 1);

      // 构建完整的URL
      let targetUrl;
      if (page.startsWith('http://') || page.startsWith('https://') || page.startsWith('/')) {
        // 绝对URL或根路径
        targetUrl = page;
      } else {
        // 相对路径，基于当前目录
        targetUrl = basePath + page;
      }

      // 添加查询参数
      if (Object.keys(params).length > 0) {
        const url = new URL(targetUrl, window.location.origin);
        Object.keys(params).forEach(key => {
          url.searchParams.set(key, params[key]);
        });
        targetUrl = url.toString();
      }

      console.log('Navigating to:', targetUrl);
      window.location.href = targetUrl;
    } catch (error) {
      console.error('Navigation error:', error);
      // 降级处理：直接跳转到页面
      window.location.href = page + (Object.keys(params).length > 0 ? '?' + new URLSearchParams(params).toString() : '');
    }
  }

  static back() {
    window.history.back();
  }

  static getParams() {
    const params = {};
    const urlParams = new URLSearchParams(window.location.search);
    for (const [key, value] of urlParams) {
      params[key] = value;
    }
    return params;
  }
}

// 日期格式化工具
class DateUtils {
  static formatDate(date, format = 'YYYY-MM-DD') {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hour = String(d.getHours()).padStart(2, '0');
    const minute = String(d.getMinutes()).padStart(2, '0');

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute);
  }

  static getRelativeTime(date) {
    const now = new Date();
    const target = new Date(date);
    const diff = now - target;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return '今天';
    if (days === 1) return '昨天';
    if (days < 7) return `${days}天前`;
    if (days < 30) return `${Math.floor(days / 7)}周前`;
    if (days < 365) return `${Math.floor(days / 30)}个月前`;
    return `${Math.floor(days / 365)}年前`;
  }
}

// UI工具
class UIUtils {
  // 显示Toast消息
  static showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      top: 100px;
      left: 50%;
      transform: translateX(-50%);
      background: ${type === 'error' ? '#FF3B30' : type === 'success' ? '#34C759' : '#007AFF'};
      color: white;
      padding: 12px 20px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      z-index: 1000;
      animation: toastSlideIn 0.3s ease-out;
    `;

    document.body.appendChild(toast);
    setTimeout(() => {
      toast.style.animation = 'toastSlideOut 0.3s ease-out';
      setTimeout(() => document.body.removeChild(toast), 300);
    }, 2000);
  }

  // 显示确认对话框
  static showConfirm(title, message, onConfirm, onCancel) {
    const overlay = document.createElement('div');
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.4);
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    const dialog = document.createElement('div');
    dialog.style.cssText = `
      background: white;
      border-radius: 14px;
      width: 270px;
      overflow: hidden;
      animation: dialogSlideIn 0.3s ease-out;
    `;

    dialog.innerHTML = `
      <div style="padding: 20px 16px 16px; text-align: center;">
        <div style="font-size: 17px; font-weight: 600; margin-bottom: 8px;">${title}</div>
        <div style="font-size: 13px; color: #666; line-height: 1.4;">${message}</div>
      </div>
      <div style="border-top: 0.5px solid #E5E5EA; display: flex;">
        <button class="cancel-btn" style="flex: 1; padding: 12px; border: none; background: none; font-size: 17px; color: #007AFF; border-right: 0.5px solid #E5E5EA;">取消</button>
        <button class="confirm-btn" style="flex: 1; padding: 12px; border: none; background: none; font-size: 17px; color: #007AFF; font-weight: 600;">确定</button>
      </div>
    `;

    overlay.appendChild(dialog);
    document.body.appendChild(overlay);

    dialog.querySelector('.cancel-btn').onclick = () => {
      document.body.removeChild(overlay);
      if (onCancel) onCancel();
    };

    dialog.querySelector('.confirm-btn').onclick = () => {
      document.body.removeChild(overlay);
      if (onConfirm) onConfirm();
    };

    overlay.onclick = (e) => {
      if (e.target === overlay) {
        document.body.removeChild(overlay);
        if (onCancel) onCancel();
      }
    };
  }

  // 显示自定义对话框（支持多个按钮）
  static showDialog(title, message, buttons = []) {
    const overlay = document.createElement('div');
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.4);
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    const dialog = document.createElement('div');
    dialog.style.cssText = `
      background: white;
      border-radius: 14px;
      width: 270px;
      overflow: hidden;
      animation: dialogSlideIn 0.3s ease-out;
    `;

    // 构建按钮HTML
    const buttonsHtml = buttons.map((button, index) => {
      const borderStyle = index < buttons.length - 1 ? 'border-right: 0.5px solid #E5E5EA;' : '';
      const fontWeight = button.primary ? 'font-weight: 600;' : '';
      return `<button class="dialog-btn" data-index="${index}" style="flex: 1; padding: 12px; border: none; background: none; font-size: 17px; color: #007AFF; ${borderStyle} ${fontWeight}">${button.text}</button>`;
    }).join('');

    dialog.innerHTML = `
      <div style="padding: 20px 16px 16px; text-align: center;">
        <div style="font-size: 17px; font-weight: 600; margin-bottom: 8px;">${title}</div>
        <div style="font-size: 13px; color: #666; line-height: 1.4;">${message}</div>
      </div>
      <div style="border-top: 0.5px solid #E5E5EA; display: flex;">
        ${buttonsHtml}
      </div>
    `;

    overlay.appendChild(dialog);
    document.body.appendChild(overlay);

    // 添加按钮事件监听
    dialog.querySelectorAll('.dialog-btn').forEach((btn, index) => {
      btn.onclick = () => {
        document.body.removeChild(overlay);
        if (buttons[index] && buttons[index].action) {
          buttons[index].action();
        }
      };
    });

    // 点击遮罩层关闭对话框
    overlay.onclick = (e) => {
      if (e.target === overlay) {
        document.body.removeChild(overlay);
      }
    };
  }

  // 更新状态栏时间
  static updateStatusBar() {
    const timeElement = document.querySelector('.status-time');
    if (timeElement) {
      const now = new Date();
      timeElement.textContent = now.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    }
  }

  // 设置活动标签
  static setActiveTab(tabName) {
    document.querySelectorAll('.tab-item').forEach(tab => {
      tab.classList.remove('active');
    });
    const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeTab) {
      activeTab.classList.add('active');
    }
  }

  // 添加页面进入动画
  static animatePageEnter() {
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
      mainContent.classList.add('animate-fade-in');
    }
  }

  // 添加元素动画
  static animateElement(element, animationType = 'fadeIn', delay = 0) {
    setTimeout(() => {
      element.classList.add(`animate-${animationType}`);
    }, delay);
  }

  // 添加涟漪效果
  static addRippleEffect(element) {
    element.classList.add('ripple');
  }

  // 平滑滚动到元素
  static scrollToElement(element, offset = 0) {
    const elementPosition = element.offsetTop - offset;
    window.scrollTo({
      top: elementPosition,
      behavior: 'smooth'
    });
  }

  // 显示加载状态（改进版）
  static showLoading(message = '加载中...') {
    const loading = document.createElement('div');
    loading.id = 'loading-overlay';
    loading.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(5px);
      ">
        <div style="
          background: white;
          padding: 30px;
          border-radius: 12px;
          text-align: center;
          min-width: 120px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
          animation: scaleIn 0.3s ease-out;
        ">
          <div style="
            width: 24px;
            height: 24px;
            border: 3px solid #007AFF;
            border-top: 3px solid transparent;
            border-radius: 50%;
            margin: 0 auto 15px;
            animation: spin 1s linear infinite;
          "></div>
          <div style="font-size: 14px; color: #333; font-weight: 500;">${message}</div>
        </div>
      </div>
    `;
    document.body.appendChild(loading);
  }

  // 隐藏加载状态（改进版）
  static hideLoading() {
    const loading = document.getElementById('loading-overlay');
    if (loading) {
      loading.style.opacity = '0';
      setTimeout(() => loading.remove(), 300);
    }
  }
}

// 游戏类型配置
const GAME_TYPES = {
  '象棋': { icon: '♟️', color: '#8B4513' },
  '围棋': { icon: '⚫', color: '#2F4F4F' },
  '国际象棋': { icon: '♛', color: '#4B0082' },
  '扑克': { icon: '🃏', color: '#DC143C' },
  '麻将': { icon: '🀄', color: '#228B22' },
  '其他': { icon: '🎯', color: '#696969' }
};

// 对局结果配置
const GAME_RESULTS = {
  '胜': { color: '#34C759', icon: '🏆' },
  '负': { color: '#FF3B30', icon: '😔' },
  '和': { color: '#FF9500', icon: '🤝' }
};

// 初始化应用
function initApp() {
  // 更新状态栏时间
  UIUtils.updateStatusBar();
  setInterval(UIUtils.updateStatusBar, 60000);

  // 添加CSS动画
  const style = document.createElement('style');
  style.textContent = `
    @keyframes toastSlideIn {
      from { opacity: 0; transform: translate(-50%, -20px); }
      to { opacity: 1; transform: translate(-50%, 0); }
    }
    @keyframes toastSlideOut {
      from { opacity: 1; transform: translate(-50%, 0); }
      to { opacity: 0; transform: translate(-50%, -20px); }
    }
    @keyframes dialogSlideIn {
      from { opacity: 0; transform: scale(0.8); }
      to { opacity: 1; transform: scale(1); }
    }
  `;
  document.head.appendChild(style);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initApp);

// 导出全局变量
window.appStorage = appStorage;
window.Navigator = Navigator;
window.DateUtils = DateUtils;
window.UIUtils = UIUtils;
window.GAME_TYPES = GAME_TYPES;
window.GAME_RESULTS = GAME_RESULTS;
