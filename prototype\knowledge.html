<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>技巧知识 - 棋牌记事本</title>
    <link rel="stylesheet" href="css/ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 技巧页面特定样式 */
        .knowledge-header {
            background: linear-gradient(135deg, var(--primary-color), #3B82F6);
            color: white;
            padding: 24px 16px;
            margin: 16px;
            border-radius: var(--border-radius-large);
            text-align: center;
        }
        
        .knowledge-title {
            font-size: var(--font-size-title2);
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .knowledge-subtitle {
            font-size: var(--font-size-subhead);
            opacity: 0.9;
        }
        
        .category-tabs {
            display: flex;
            background: var(--surface-color);
            margin: 0 16px;
            border-radius: var(--border-radius-medium);
            padding: 4px;
            box-shadow: var(--shadow-small);
            overflow-x: auto;
        }
        
        .category-tab {
            flex: 1;
            background: none;
            border: none;
            padding: 12px 16px;
            border-radius: var(--border-radius-small);
            font-size: var(--font-size-subhead);
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--animation-fast);
            white-space: nowrap;
            min-width: 80px;
        }
        
        .category-tab.active {
            background: var(--primary-color);
            color: white;
        }
        
        .knowledge-content {
            padding: 16px;
        }
        
        .knowledge-item {
            background: var(--surface-color);
            border-radius: var(--border-radius-medium);
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: var(--shadow-small);
            cursor: pointer;
            transition: all var(--animation-fast);
        }
        
        .knowledge-item:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }
        
        .knowledge-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }
        
        .knowledge-item-title {
            font-size: var(--font-size-body);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .knowledge-item-category {
            background: var(--ios-gray6);
            color: var(--text-secondary);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-caption1);
            font-weight: 500;
        }
        
        .knowledge-item-preview {
            color: var(--text-secondary);
            font-size: var(--font-size-subhead);
            line-height: 1.4;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .knowledge-item-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: var(--font-size-caption1);
            color: var(--text-tertiary);
        }
        
        .knowledge-item-game {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .knowledge-item-actions {
            display: flex;
            gap: 12px;
        }
        
        .action-icon {
            color: var(--text-tertiary);
            cursor: pointer;
            transition: color var(--animation-fast);
        }
        
        .action-icon:hover {
            color: var(--primary-color);
        }
        
        .action-icon.favorited {
            color: var(--secondary-color);
        }
        
        .floating-add-btn {
            position: fixed;
            bottom: calc(var(--tab-bar-height) + 16px);
            right: 16px;
            width: 56px;
            height: 56px;
            background: var(--primary-color);
            border: none;
            border-radius: 28px;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
            transition: all var(--animation-fast);
            z-index: 100;
        }
        
        .floating-add-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(30, 58, 138, 0.4);
        }
        
        .search-section {
            padding: 16px;
            background: var(--surface-color);
            border-bottom: 0.5px solid var(--border-color);
        }
        
        .search-container {
            position: relative;
        }
        
        .search-input {
            width: 100%;
            background: var(--ios-gray6);
            border: none;
            border-radius: 10px;
            padding: 8px 12px 8px 36px;
            font-size: var(--font-size-body);
            color: var(--text-primary);
        }
        
        .search-input::placeholder {
            color: var(--text-tertiary);
        }
        
        .search-input:focus {
            outline: none;
            background: var(--surface-color);
            box-shadow: 0 0 0 2px var(--primary-color);
        }
        
        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-tertiary);
            font-size: 14px;
            pointer-events: none;
        }
        
        .empty-knowledge {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }
        
        .empty-knowledge-icon {
            font-size: 64px;
            color: var(--ios-gray3);
            margin-bottom: 20px;
        }
        
        .empty-knowledge-title {
            font-size: var(--font-size-title3);
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }
        
        .empty-knowledge-text {
            font-size: var(--font-size-body);
            margin-bottom: 24px;
            line-height: 1.5;
        }
        
        .empty-knowledge-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius-medium);
            padding: 12px 24px;
            font-size: var(--font-size-body);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--animation-fast);
        }
        
        .empty-knowledge-btn:hover {
            background: #1E40AF;
        }
        
        .difficulty-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: var(--font-size-caption2);
            font-weight: 500;
        }
        
        .difficulty-easy {
            color: var(--success-color);
        }
        
        .difficulty-medium {
            color: var(--warning-color);
        }
        
        .difficulty-hard {
            color: var(--error-color);
        }
        
        .knowledge-stats {
            display: flex;
            justify-content: space-around;
            background: var(--surface-color);
            margin: 0 16px 16px;
            padding: 16px;
            border-radius: var(--border-radius-medium);
            box-shadow: var(--shadow-small);
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: var(--font-size-title3);
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-caption1);
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="device-screen">
            <!-- 技巧知识页面 -->
            <div id="knowledge" class="page">
                <!-- 状态栏 -->
                <div class="status-bar">
                    <div class="status-left">
                        <span class="status-time">14:30</span>
                    </div>
                    <div class="status-center">
                        <i class="fas fa-signal" style="font-size: 10px;"></i>
                        <i class="fas fa-wifi" style="font-size: 10px; margin-left: 4px;"></i>
                    </div>
                    <div class="status-right">
                        <i class="fas fa-battery-three-quarters" style="font-size: 12px;"></i>
                        <span style="font-size: 11px; margin-left: 2px;">85%</span>
                    </div>
                </div>

                <!-- 导航栏 -->
                <div class="nav-bar">
                    <button class="nav-button" data-action="back">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="nav-title">技巧知识</div>
                    <button class="nav-button primary" data-action="add-knowledge">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>

                <!-- 主内容 -->
                <div class="main-content">
                    <!-- 头部介绍 -->
                    <div class="knowledge-header">
                        <div class="knowledge-title">技巧宝库</div>
                        <div class="knowledge-subtitle">收集整理您的棋牌心得</div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="knowledge-stats">
                        <div class="stat-item">
                            <div class="stat-number">24</div>
                            <div class="stat-label">技巧总数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">8</div>
                            <div class="stat-label">收藏数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">5</div>
                            <div class="stat-label">分类数</div>
                        </div>
                    </div>

                    <!-- 搜索栏 -->
                    <div class="search-section">
                        <div class="search-container">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="搜索技巧知识..." id="knowledge-search">
                        </div>
                    </div>

                    <!-- 分类标签 -->
                    <div class="category-tabs">
                        <button class="category-tab active" data-category="all">全部</button>
                        <button class="category-tab" data-category="开局">开局</button>
                        <button class="category-tab" data-category="中局">中局</button>
                        <button class="category-tab" data-category="残局">残局</button>
                        <button class="category-tab" data-category="技巧">技巧</button>
                        <button class="category-tab" data-category="心得">心得</button>
                    </div>

                    <!-- 知识列表 -->
                    <div class="knowledge-content">
                        <!-- 示例知识项 -->
                        <div class="knowledge-item" data-action="view-knowledge" data-id="1">
                            <div class="knowledge-item-header">
                                <div>
                                    <div class="knowledge-item-title">象棋开局基本原则</div>
                                    <div class="knowledge-item-category">开局</div>
                                </div>
                            </div>
                            <div class="knowledge-item-preview">
                                开局阶段要注意子力发展和阵型协调，避免过早出动主力，要先发展轻子再出重子...
                            </div>
                            <div class="knowledge-item-meta">
                                <div class="knowledge-item-game">
                                    <i class="fas fa-chess"></i>
                                    <span>象棋</span>
                                    <span class="difficulty-badge difficulty-easy">
                                        <i class="fas fa-star"></i>
                                        初级
                                    </span>
                                </div>
                                <div class="knowledge-item-actions">
                                    <i class="fas fa-heart action-icon favorited"></i>
                                    <i class="fas fa-share action-icon"></i>
                                </div>
                            </div>
                        </div>

                        <div class="knowledge-item" data-action="view-knowledge" data-id="2">
                            <div class="knowledge-item-header">
                                <div>
                                    <div class="knowledge-item-title">围棋布局要点</div>
                                    <div class="knowledge-item-category">布局</div>
                                </div>
                            </div>
                            <div class="knowledge-item-preview">
                                布局阶段要重视角部和边部的价值，金角银边草肚皮，优先占据角部要点...
                            </div>
                            <div class="knowledge-item-meta">
                                <div class="knowledge-item-game">
                                    <i class="fas fa-circle"></i>
                                    <span>围棋</span>
                                    <span class="difficulty-badge difficulty-medium">
                                        <i class="fas fa-star"></i>
                                        中级
                                    </span>
                                </div>
                                <div class="knowledge-item-actions">
                                    <i class="fas fa-heart action-icon"></i>
                                    <i class="fas fa-share action-icon"></i>
                                </div>
                            </div>
                        </div>

                        <div class="knowledge-item" data-action="view-knowledge" data-id="3">
                            <div class="knowledge-item-header">
                                <div>
                                    <div class="knowledge-item-title">国际象棋战术组合</div>
                                    <div class="knowledge-item-category">技巧</div>
                                </div>
                            </div>
                            <div class="knowledge-item-preview">
                                掌握基本的战术组合如叉攻、牵制、闪击等，能够在实战中创造优势...
                            </div>
                            <div class="knowledge-item-meta">
                                <div class="knowledge-item-game">
                                    <i class="fas fa-chess-king"></i>
                                    <span>国际象棋</span>
                                    <span class="difficulty-badge difficulty-hard">
                                        <i class="fas fa-star"></i>
                                        高级
                                    </span>
                                </div>
                                <div class="knowledge-item-actions">
                                    <i class="fas fa-heart action-icon"></i>
                                    <i class="fas fa-share action-icon"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 空状态（当没有知识时显示） -->
                        <div class="empty-knowledge" id="empty-knowledge" style="display: none;">
                            <div class="empty-knowledge-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="empty-knowledge-title">暂无技巧知识</div>
                            <div class="empty-knowledge-text">
                                开始记录您的棋牌心得<br>
                                积累宝贵的实战经验
                            </div>
                            <button class="empty-knowledge-btn" data-action="add-knowledge">
                                <i class="fas fa-plus"></i>
                                添加技巧
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 浮动添加按钮 -->
                <button class="floating-add-btn" data-action="add-knowledge">
                    <i class="fas fa-plus"></i>
                </button>

                <!-- 标签栏 -->
                <div class="tab-bar">
                    <div class="tab-item" data-page="home">
                        <div class="tab-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="tab-label">首页</div>
                    </div>
                    <div class="tab-item" data-page="records">
                        <div class="tab-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="tab-label">记录</div>
                    </div>
                    <div class="tab-item" data-page="stats">
                        <div class="tab-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="tab-label">统计</div>
                    </div>
                    <div class="tab-item active" data-page="knowledge">
                        <div class="tab-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="tab-label">技巧</div>
                    </div>
                    <div class="tab-item" data-page="profile">
                        <div class="tab-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="tab-label">我的</div>
                    </div>
                </div>

                <!-- Home Indicator -->
                <div class="home-indicator"></div>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script>
        // 技巧页面特定功能
        document.addEventListener('DOMContentLoaded', function() {
            // 分类切换
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    const category = this.dataset.category;
                    console.log('切换分类:', category);
                });
            });

            // 收藏功能
            document.querySelectorAll('.fa-heart').forEach(heart => {
                heart.addEventListener('click', function(e) {
                    e.stopPropagation();
                    this.classList.toggle('favorited');
                });
            });

            // 搜索功能
            const searchInput = document.getElementById('knowledge-search');
            searchInput.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                console.log('搜索技巧:', query);
            });
        });
    </script>
</body>
</html>
