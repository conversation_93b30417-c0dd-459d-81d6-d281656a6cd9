<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对局详情</title>
    <link rel="stylesheet" href="ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .detail-header {
            background: linear-gradient(135deg, var(--primary-color), var(--ios-blue));
            color: white;
            padding: var(--spacing-lg) var(--spacing-md);
            margin: 0 var(--spacing-md) var(--spacing-md);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-md);
        }

        .game-info {
            flex: 1;
        }

        .game-type-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: var(--spacing-sm);
        }

        .opponent-name {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: var(--spacing-xs);
        }

        .game-date {
            font-size: 14px;
            opacity: 0.9;
        }

        .result-badge {
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-lg);
            font-size: 16px;
            font-weight: 700;
            text-align: center;
            min-width: 60px;
        }

        .result-badge.win { background: var(--ios-green); }
        .result-badge.lose { background: var(--ios-red); }
        .result-badge.draw { background: var(--ios-orange); }

        .header-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .detail-section {
            background: white;
            margin: 0 var(--spacing-md) var(--spacing-md);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
        }

        .section-header {
            padding: var(--spacing-md);
            border-bottom: 0.5px solid var(--ios-gray5);
            background: var(--ios-gray6);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .section-content {
            padding: var(--spacing-md);
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) 0;
            border-bottom: 0.5px solid var(--ios-gray5);
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .info-value {
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
            text-align: right;
            flex: 1;
            margin-left: var(--spacing-md);
        }

        .notes-content {
            font-size: 15px;
            line-height: 1.6;
            color: var(--text-primary);
            white-space: pre-wrap;
        }

        .moves-content {
            font-size: 14px;
            line-height: 1.6;
            color: var(--text-primary);
            font-family: 'Monaco', 'Menlo', monospace;
            background: var(--ios-gray6);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            white-space: pre-wrap;
        }

        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
        }

        .tag-chip {
            background: var(--ios-blue);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .importance-stars {
            display: flex;
            gap: 2px;
        }

        .importance-star {
            color: var(--secondary-color);
            font-size: 16px;
        }

        .action-buttons {
            display: flex;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
        }

        .action-button {
            flex: 1;
            padding: var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
        }

        .edit-button {
            background: var(--ios-blue);
            color: white;
        }

        .edit-button:hover {
            background: #0056CC;
        }

        .copy-button {
            background: var(--ios-purple);
            color: white;
        }

        .copy-button:hover {
            background: #8E44AD;
        }

        .delete-button {
            background: var(--ios-red);
            color: white;
        }

        .delete-button:hover {
            background: #CC0000;
        }



        .empty-content {
            text-align: center;
            color: var(--text-secondary);
            font-style: italic;
            padding: var(--spacing-lg) 0;
        }


    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                    </div>
                    <div class="wifi-icon"></div>
                    <span>100%</span>
                    <div class="battery-icon"></div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <button class="nav-button" onclick="Navigator.back()">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">对局详情</div>
                <button class="nav-button" onclick="editRecord()">
                    编辑
                </button>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <!-- 详情头部 -->
                <div class="detail-header" id="detailHeader">
                    <!-- 动态加载头部信息 -->
                </div>

                <!-- 基本信息 -->
                <div class="detail-section">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-info-circle"></i>
                            基本信息
                        </div>
                    </div>
                    <div class="section-content" id="basicInfo">
                        <!-- 动态加载基本信息 -->
                    </div>
                </div>

                <!-- 对局笔记 -->
                <div class="detail-section" id="notesSection">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-sticky-note"></i>
                            对局笔记
                        </div>
                    </div>
                    <div class="section-content">
                        <div class="notes-content" id="notesContent">
                            <!-- 动态加载笔记内容 -->
                        </div>
                    </div>
                </div>

                <!-- 棋谱记录 -->
                <div class="detail-section" id="movesSection">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-chess"></i>
                            棋谱记录
                        </div>
                    </div>
                    <div class="section-content">
                        <div class="moves-content" id="movesContent">
                            <!-- 动态加载棋谱内容 -->
                        </div>
                    </div>
                </div>

                <!-- 标签 -->
                <div class="detail-section" id="tagsSection">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-tags"></i>
                            标签
                        </div>
                    </div>
                    <div class="section-content">
                        <div class="tags-container" id="tagsContainer">
                            <!-- 动态加载标签 -->
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="action-button edit-button" onclick="editRecord()">
                        <i class="fas fa-edit"></i>
                        编辑
                    </button>
                    <button class="action-button copy-button" onclick="copyRecord()">
                        <i class="fas fa-copy"></i>
                        复制
                    </button>

                    <button class="action-button delete-button" onclick="deleteRecord()">
                        <i class="fas fa-trash"></i>
                        删除
                    </button>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="app-utils.js"></script>
    <script>
        let currentRecord = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            const params = Navigator.getParams();
            const recordId = params.id;
            
            if (recordId) {
                loadRecord(recordId);
            } else {
                UIUtils.showToast('记录不存在', 'error');
                Navigator.back();
            }
        });

        // 加载记录详情
        function loadRecord(recordId) {
            const records = appStorage.get('gameRecords') || [];
            currentRecord = records.find(record => record.id === recordId);
            
            if (!currentRecord) {
                UIUtils.showToast('记录不存在', 'error');
                Navigator.back();
                return;
            }

            renderRecord();
        }

        // 渲染记录详情
        function renderRecord() {
            renderHeader();
            renderBasicInfo();
            renderNotes();
            renderMoves();
            renderTags();
        }

        // 渲染头部信息
        function renderHeader() {
            const header = document.getElementById('detailHeader');
            const gameType = GAME_TYPES[currentRecord.gameType] || GAME_TYPES['其他'];
            
            header.innerHTML = `
                <div class="header-top">
                    <div class="game-info">
                        <div class="game-type-badge">
                            <span>${gameType.icon}</span>
                            <span>${currentRecord.gameType}</span>
                        </div>
                        <div class="opponent-name">vs ${currentRecord.opponent}</div>
                        <div class="game-date">${DateUtils.formatDate(currentRecord.date, 'YYYY年MM月DD日 HH:mm')}</div>
                    </div>
                    <div class="result-badge ${currentRecord.result === '胜' ? 'win' : currentRecord.result === '负' ? 'lose' : 'draw'}">
                        ${currentRecord.result}
                    </div>
                </div>
                <div class="header-stats">
                    <div class="stat-item">
                        <div class="stat-value">${currentRecord.duration || '未记录'}</div>
                        <div class="stat-label">用时</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${currentRecord.location || '未记录'}</div>
                        <div class="stat-label">地点</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">
                            <div class="importance-stars">
                                ${Array(currentRecord.importance || 1).fill('⭐').join('')}
                            </div>
                        </div>
                        <div class="stat-label">重要程度</div>
                    </div>
                </div>
            `;
        }

        // 渲染基本信息
        function renderBasicInfo() {
            const container = document.getElementById('basicInfo');
            container.innerHTML = `
                <div class="info-row">
                    <div class="info-label">游戏类型</div>
                    <div class="info-value">${currentRecord.gameType}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">对手</div>
                    <div class="info-value">${currentRecord.opponent}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">对局时间</div>
                    <div class="info-value">${DateUtils.formatDate(currentRecord.date, 'YYYY-MM-DD HH:mm')}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">对局结果</div>
                    <div class="info-value">${currentRecord.result}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">对局用时</div>
                    <div class="info-value">${currentRecord.duration || '未记录'}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">对局地点</div>
                    <div class="info-value">${currentRecord.location || '未记录'}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">对局性质</div>
                    <div class="info-value">${currentRecord.gameNature || '普通对局'}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">创建时间</div>
                    <div class="info-value">${DateUtils.formatDate(currentRecord.createdAt, 'YYYY-MM-DD HH:mm')}</div>
                </div>
            `;
        }

        // 渲染笔记
        function renderNotes() {
            const section = document.getElementById('notesSection');
            const content = document.getElementById('notesContent');
            
            if (currentRecord.notes && currentRecord.notes.trim()) {
                content.textContent = currentRecord.notes;
                section.style.display = 'block';
            } else {
                content.innerHTML = '<div class="empty-content">暂无对局笔记</div>';
                section.style.display = 'block';
            }
        }

        // 渲染棋谱
        function renderMoves() {
            const section = document.getElementById('movesSection');
            const content = document.getElementById('movesContent');
            
            if (currentRecord.moves && currentRecord.moves.trim()) {
                content.textContent = currentRecord.moves;
                section.style.display = 'block';
            } else {
                content.innerHTML = '<div class="empty-content">暂无棋谱记录</div>';
                section.style.display = 'block';
            }
        }

        // 渲染标签
        function renderTags() {
            const section = document.getElementById('tagsSection');
            const container = document.getElementById('tagsContainer');
            
            if (currentRecord.tags && currentRecord.tags.length > 0) {
                container.innerHTML = currentRecord.tags.map(tag => 
                    `<span class="tag-chip">${tag}</span>`
                ).join('');
                section.style.display = 'block';
            } else {
                container.innerHTML = '<div class="empty-content">暂无标签</div>';
                section.style.display = 'block';
            }
        }

        // 编辑记录
        function editRecord() {
            // 暂时使用添加记录页面进行编辑
            Navigator.navigate('add-record.html', {
                edit: true,
                id: currentRecord.id,
                gameType: currentRecord.gameType,
                opponent: currentRecord.opponent,
                result: currentRecord.result,
                importance: currentRecord.importance,
                notes: currentRecord.notes,
                tags: currentRecord.tags?.join(',') || ''
            });
        }

        // 复制记录
        function copyRecord() {
            if (!currentRecord) return;

            const copyText = formatRecordText(currentRecord);

            if (navigator.clipboard) {
                navigator.clipboard.writeText(copyText).then(() => {
                    UIUtils.showToast('记录已复制到剪贴板', 'success');
                }).catch(err => {
                    console.log('复制失败:', err);
                    fallbackCopy(copyText);
                });
            } else {
                fallbackCopy(copyText);
            }
        }



        // 格式化记录文本
        function formatRecordText(record) {
            return `🎯 ${record.gameType} 对局记录

👤 对手：${record.opponent}
🏆 结果：${record.result}
📅 日期：${DateUtils.formatDate(record.date, 'YYYY-MM-DD HH:mm')}
${record.duration ? `⏱️ 时长：${record.duration}\n` : ''}${record.location ? `📍 地点：${record.location}\n` : ''}${record.importance > 1 ? `⭐ 重要性：${Array(record.importance).fill('⭐').join('')}\n` : ''}${record.notes ? `📝 笔记：${record.notes}\n` : ''}${record.tags && record.tags.length > 0 ? `🏷️ 标签：${record.tags.join(', ')}\n` : ''}
---
来自棋牌笔记本`;
        }

        function fallbackCopy(text) {
            // 创建临时文本区域
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();

            try {
                document.execCommand('copy');
                UIUtils.showToast('内容已复制到剪贴板', 'success');
            } catch (err) {
                UIUtils.showToast('复制失败', 'error');
            }

            document.body.removeChild(textArea);
        }

        // 删除记录
        function deleteRecord() {
            UIUtils.showConfirm(
                '删除记录',
                '确定要删除这条对局记录吗？此操作不可撤销。',
                () => {
                    const deletedRecord = appStorage.deleteRecord(currentRecord.id);
                    if (deletedRecord) {
                        UIUtils.showToast('记录已删除', 'success');
                        setTimeout(() => {
                            Navigator.navigate('records.html');
                        }, 1000);
                    } else {
                        UIUtils.showToast('删除失败', 'error');
                    }
                }
            );
        }

        // 查看对手详情
        function viewOpponentDetail() {
            if (currentRecord && currentRecord.opponent) {
                Navigator.navigate('opponent-detail.html', { name: currentRecord.opponent });
            }
        }

        // 添加相似记录
        function addSimilarRecord() {
            if (currentRecord) {
                Navigator.navigate('add-record.html', {
                    gameType: currentRecord.gameType,
                    opponent: currentRecord.opponent,
                    location: currentRecord.location
                });
            }
        }

        // 分享记录
        function shareRecord() {
            if (!currentRecord) return;

            const shareText = `我在${DateUtils.formatDate(currentRecord.date, 'YYYY-MM-DD')}与${currentRecord.opponent}进行了一局${currentRecord.gameType}，结果：${currentRecord.result}。${currentRecord.notes ? '\n笔记：' + currentRecord.notes : ''}`;

            if (navigator.share) {
                navigator.share({
                    title: '对局记录分享',
                    text: shareText
                }).catch(err => {
                    console.log('分享失败:', err);
                    copyToClipboard(shareText);
                });
            } else {
                copyToClipboard(shareText);
            }
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.opacity = '0';
            document.body.appendChild(textArea);
            textArea.select();

            try {
                document.execCommand('copy');
                UIUtils.showToast('内容已复制到剪贴板', 'success');
            } catch (err) {
                UIUtils.showToast('复制失败', 'error');
            }

            document.body.removeChild(textArea);
        }
    </script>
</body>
</html>
