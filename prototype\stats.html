<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>统计分析 - 棋牌记事本</title>
    <link rel="stylesheet" href="css/ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 统计页面特定样式 */
        .stats-overview {
            background: linear-gradient(135deg, var(--primary-color), #3B82F6);
            color: white;
            padding: 24px 16px;
            margin: 16px;
            border-radius: var(--border-radius-large);
        }
        
        .stats-title {
            font-size: var(--font-size-title2);
            font-weight: 700;
            margin-bottom: 16px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .stat-box {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius-medium);
            padding: 16px;
        }
        
        .stat-number {
            font-size: var(--font-size-title1);
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: var(--font-size-subhead);
            opacity: 0.9;
        }
        
        .section {
            margin: 16px;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .section-title {
            font-size: var(--font-size-title3);
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .section-action {
            color: var(--ios-blue);
            font-size: var(--font-size-subhead);
            text-decoration: none;
            font-weight: 500;
        }
        
        .stats-card {
            background: var(--surface-color);
            border-radius: var(--border-radius-medium);
            padding: 16px;
            box-shadow: var(--shadow-small);
        }
        
        .stat-item {
            padding: 12px 0;
            border-bottom: 1px solid var(--ios-gray5);
        }
        
        .stat-item:last-child {
            border-bottom: none;
        }
        
        .stat-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }
        
        .stat-item-title {
            font-size: var(--font-size-body);
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .stat-item-value {
            font-size: var(--font-size-body);
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .stat-item-subtitle {
            font-size: var(--font-size-subhead);
            color: var(--text-secondary);
            margin-bottom: 8px;
        }
        
        .progress-bar {
            height: 4px;
            background: var(--ios-gray5);
            border-radius: 2px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: var(--primary-color);
            border-radius: 2px;
            transition: width var(--animation-normal);
        }
        
        .chart-container {
            height: 200px;
            background: var(--ios-gray6);
            border-radius: var(--border-radius-medium);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: var(--font-size-subhead);
            margin-top: 12px;
        }
        
        .achievement-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 12px;
        }
        
        .achievement-item {
            background: var(--surface-color);
            border-radius: var(--border-radius-medium);
            padding: 16px;
            text-align: center;
            box-shadow: var(--shadow-small);
        }
        
        .achievement-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }
        
        .achievement-icon.unlocked {
            color: var(--secondary-color);
        }
        
        .achievement-icon.locked {
            color: var(--ios-gray3);
        }
        
        .achievement-title {
            font-size: var(--font-size-subhead);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .achievement-desc {
            font-size: var(--font-size-caption1);
            color: var(--text-secondary);
        }
        
        .time-filter {
            display: flex;
            background: var(--ios-gray6);
            border-radius: var(--border-radius-small);
            padding: 2px;
            margin-bottom: 16px;
        }
        
        .time-filter-btn {
            flex: 1;
            background: none;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: var(--font-size-subhead);
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--animation-fast);
        }
        
        .time-filter-btn.active {
            background: var(--surface-color);
            color: var(--text-primary);
            box-shadow: var(--shadow-small);
        }
        
        .opponent-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .opponent-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--ios-gray5);
        }
        
        .opponent-item:last-child {
            border-bottom: none;
        }
        
        .opponent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 12px;
        }
        
        .opponent-info {
            flex: 1;
        }
        
        .opponent-name {
            font-size: var(--font-size-body);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .opponent-record {
            font-size: var(--font-size-subhead);
            color: var(--text-secondary);
        }
        
        .opponent-winrate {
            font-size: var(--font-size-subhead);
            font-weight: 600;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="device-screen">
            <!-- 统计页面 -->
            <div id="stats" class="page">
                <!-- 状态栏 -->
                <div class="status-bar">
                    <div class="status-left">
                        <span class="status-time">14:30</span>
                    </div>
                    <div class="status-center">
                        <i class="fas fa-signal" style="font-size: 10px;"></i>
                        <i class="fas fa-wifi" style="font-size: 10px; margin-left: 4px;"></i>
                    </div>
                    <div class="status-right">
                        <i class="fas fa-battery-three-quarters" style="font-size: 12px;"></i>
                        <span style="font-size: 11px; margin-left: 2px;">85%</span>
                    </div>
                </div>

                <!-- 导航栏 -->
                <div class="nav-bar">
                    <button class="nav-button" data-action="back">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="nav-title">统计分析</div>
                    <button class="nav-button" data-action="export">
                        <i class="fas fa-share"></i>
                    </button>
                </div>

                <!-- 主内容 -->
                <div class="main-content">
                    <!-- 总体统计 -->
                    <div class="stats-overview">
                        <div class="stats-title">总体表现</div>
                        <div class="stats-grid">
                            <div class="stat-box">
                                <div class="stat-number" id="stats-total-games">0</div>
                                <div class="stat-label">总对局</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number" id="stats-win-rate">0%</div>
                                <div class="stat-label">胜率</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number" id="stats-wins">0</div>
                                <div class="stat-label">胜局</div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-number" id="stats-losses">0</div>
                                <div class="stat-label">负局</div>
                            </div>
                        </div>
                    </div>

                    <!-- 游戏类型统计 -->
                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">游戏类型</div>
                            <a href="#" class="section-action">详细</a>
                        </div>
                        <div class="stats-card">
                            <div id="game-type-stats">
                                <!-- 动态加载游戏类型统计 -->
                                <div class="stat-item">
                                    <div class="stat-item-header">
                                        <span class="stat-item-title">象棋</span>
                                        <span class="stat-item-value">75%</span>
                                    </div>
                                    <div class="stat-item-subtitle">8局 • 6胜</div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 75%"></div>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-item-header">
                                        <span class="stat-item-title">围棋</span>
                                        <span class="stat-item-value">60%</span>
                                    </div>
                                    <div class="stat-item-subtitle">5局 • 3胜</div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 60%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 趋势分析 -->
                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">趋势分析</div>
                        </div>
                        <div class="stats-card">
                            <div class="time-filter">
                                <button class="time-filter-btn active">本周</button>
                                <button class="time-filter-btn">本月</button>
                                <button class="time-filter-btn">本年</button>
                            </div>
                            <div class="chart-container">
                                <div>
                                    <i class="fas fa-chart-line" style="font-size: 32px; margin-bottom: 8px;"></i>
                                    <div>胜率趋势图</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 对手分析 -->
                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">对手分析</div>
                            <a href="#" class="section-action">查看全部</a>
                        </div>
                        <div class="stats-card">
                            <div class="opponent-list">
                                <div class="opponent-item">
                                    <div class="opponent-avatar">张</div>
                                    <div class="opponent-info">
                                        <div class="opponent-name">张三</div>
                                        <div class="opponent-record">8局对战</div>
                                    </div>
                                    <div class="opponent-winrate">62.5%</div>
                                </div>
                                <div class="opponent-item">
                                    <div class="opponent-avatar">李</div>
                                    <div class="opponent-info">
                                        <div class="opponent-name">李四</div>
                                        <div class="opponent-record">5局对战</div>
                                    </div>
                                    <div class="opponent-winrate">40%</div>
                                </div>
                                <div class="opponent-item">
                                    <div class="opponent-avatar">王</div>
                                    <div class="opponent-info">
                                        <div class="opponent-name">王五</div>
                                        <div class="opponent-record">3局对战</div>
                                    </div>
                                    <div class="opponent-winrate">66.7%</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 成就系统 -->
                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">成就徽章</div>
                        </div>
                        <div class="achievement-grid">
                            <div class="achievement-item">
                                <div class="achievement-icon unlocked">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <div class="achievement-title">初出茅庐</div>
                                <div class="achievement-desc">完成首局对战</div>
                            </div>
                            <div class="achievement-item">
                                <div class="achievement-icon unlocked">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="achievement-title">连胜达人</div>
                                <div class="achievement-desc">连胜3局</div>
                            </div>
                            <div class="achievement-item">
                                <div class="achievement-icon locked">
                                    <i class="fas fa-crown"></i>
                                </div>
                                <div class="achievement-title">棋王</div>
                                <div class="achievement-desc">胜率达到80%</div>
                            </div>
                            <div class="achievement-item">
                                <div class="achievement-icon locked">
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="achievement-title">百战老将</div>
                                <div class="achievement-desc">完成100局对战</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标签栏 -->
                <div class="tab-bar">
                    <div class="tab-item" data-page="home">
                        <div class="tab-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="tab-label">首页</div>
                    </div>
                    <div class="tab-item" data-page="records">
                        <div class="tab-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="tab-label">记录</div>
                    </div>
                    <div class="tab-item active" data-page="stats">
                        <div class="tab-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="tab-label">统计</div>
                    </div>
                    <div class="tab-item" data-page="knowledge">
                        <div class="tab-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="tab-label">技巧</div>
                    </div>
                    <div class="tab-item" data-page="profile">
                        <div class="tab-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="tab-label">我的</div>
                    </div>
                </div>

                <!-- Home Indicator -->
                <div class="home-indicator"></div>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
</body>
</html>
