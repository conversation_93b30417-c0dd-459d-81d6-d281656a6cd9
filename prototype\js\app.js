// 棋牌记事本 App JavaScript

class ChessNotebookApp {
  constructor() {
    this.currentPage = 'home';
    this.gameRecords = this.loadGameRecords();
    this.opponents = this.loadOpponents();
    this.knowledge = this.loadKnowledge();
    this.tags = this.loadTags();
    this.init();
  }

  init() {
    this.updateStatusBar();
    this.bindEvents();
    this.showPage('home');
    setInterval(() => this.updateStatusBar(), 1000);
  }

  // 状态栏更新
  updateStatusBar() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
    
    const statusTime = document.querySelector('.status-time');
    if (statusTime) {
      statusTime.textContent = timeString;
    }
  }

  // 事件绑定
  bindEvents() {
    // 标签栏切换
    document.querySelectorAll('.tab-item').forEach(tab => {
      tab.addEventListener('click', (e) => {
        const page = tab.dataset.page;
        this.showPage(page);
      });
    });

    // 导航按钮
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('nav-button')) {
        const action = e.target.dataset.action;
        this.handleNavAction(action);
      }
    });

    // 列表项点击
    document.addEventListener('click', (e) => {
      if (e.target.closest('.list-item')) {
        const item = e.target.closest('.list-item');
        const action = item.dataset.action;
        const id = item.dataset.id;
        this.handleListItemClick(action, id);
      }
    });

    // 按钮点击
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('btn')) {
        const action = e.target.dataset.action;
        this.handleButtonClick(action, e.target);
      }
    });
  }

  // 页面切换
  showPage(pageId) {
    // 隐藏所有页面
    document.querySelectorAll('.page').forEach(page => {
      page.classList.add('hidden');
    });

    // 显示目标页面
    const targetPage = document.getElementById(pageId);
    if (targetPage) {
      targetPage.classList.remove('hidden');
      this.currentPage = pageId;
    }

    // 更新标签栏状态
    document.querySelectorAll('.tab-item').forEach(tab => {
      tab.classList.remove('active');
      if (tab.dataset.page === pageId) {
        tab.classList.add('active');
      }
    });

    // 页面特定初始化
    this.initPage(pageId);
  }

  // 页面初始化
  initPage(pageId) {
    switch(pageId) {
      case 'home':
        this.renderHomeStats();
        this.renderRecentRecords();
        break;
      case 'records':
        this.renderRecordsList();
        break;
      case 'stats':
        this.renderStatsPage();
        break;
      case 'knowledge':
        this.renderKnowledgeList();
        break;
      case 'profile':
        this.renderProfilePage();
        break;
    }
  }

  // 导航操作处理
  handleNavAction(action) {
    switch(action) {
      case 'add-record':
        this.showPage('add-record');
        break;
      case 'search':
        this.showSearchPage();
        break;
      case 'back':
        this.goBack();
        break;
      case 'save':
        this.saveCurrentForm();
        break;
      case 'edit':
        this.editCurrentItem();
        break;
    }
  }

  // 列表项点击处理
  handleListItemClick(action, id) {
    switch(action) {
      case 'view-record':
        this.showRecordDetail(id);
        break;
      case 'view-opponent':
        this.showOpponentDetail(id);
        break;
      case 'view-knowledge':
        this.showKnowledgeDetail(id);
        break;
    }
  }

  // 按钮点击处理
  handleButtonClick(action, button) {
    switch(action) {
      case 'add-record':
        this.showPage('add-record');
        break;
      case 'quick-record':
        this.showQuickRecordModal();
        break;
      case 'view-all-records':
        this.showPage('records');
        break;
      case 'add-knowledge':
        this.showPage('add-knowledge');
        break;
    }
  }

  // 渲染首页统计
  renderHomeStats() {
    const totalGames = this.gameRecords.length;
    const wins = this.gameRecords.filter(r => r.result === 'win').length;
    const winRate = totalGames > 0 ? Math.round((wins / totalGames) * 100) : 0;

    document.getElementById('total-games').textContent = totalGames;
    document.getElementById('win-rate').textContent = `${winRate}%`;
    document.getElementById('total-opponents').textContent = this.opponents.length;
  }

  // 渲染最近记录
  renderRecentRecords() {
    const recentRecords = this.gameRecords
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 3);

    const container = document.getElementById('recent-records');
    if (!container) return;

    container.innerHTML = recentRecords.map(record => `
      <div class="list-item" data-action="view-record" data-id="${record.id}">
        <div class="list-item-icon">
          <i class="fas fa-chess"></i>
        </div>
        <div class="list-item-content">
          <div class="list-item-title">${record.gameType} vs ${record.opponent}</div>
          <div class="list-item-subtitle">${this.formatDate(record.date)} • ${this.getResultText(record.result)}</div>
        </div>
        <div class="list-item-chevron">
          <i class="fas fa-chevron-right"></i>
        </div>
      </div>
    `).join('');
  }

  // 渲染记录列表
  renderRecordsList() {
    const container = document.getElementById('records-list');
    if (!container) return;

    const sortedRecords = this.gameRecords
      .sort((a, b) => new Date(b.date) - new Date(a.date));

    container.innerHTML = sortedRecords.map(record => `
      <div class="list-item" data-action="view-record" data-id="${record.id}">
        <div class="list-item-icon">
          <i class="fas fa-chess"></i>
        </div>
        <div class="list-item-content">
          <div class="list-item-title">${record.gameType} vs ${record.opponent}</div>
          <div class="list-item-subtitle">${this.formatDate(record.date)} • ${this.getResultText(record.result)}</div>
        </div>
        <div class="list-item-chevron">
          <i class="fas fa-chevron-right"></i>
        </div>
      </div>
    `).join('');
  }

  // 渲染统计页面
  renderStatsPage() {
    this.renderOverallStats();
    this.renderGameTypeStats();
    this.renderMonthlyTrend();
    this.renderOpponentStats();
  }

  // 渲染总体统计
  renderOverallStats() {
    const totalGames = this.gameRecords.length;
    const wins = this.gameRecords.filter(r => r.result === 'win').length;
    const losses = this.gameRecords.filter(r => r.result === 'loss').length;
    const draws = this.gameRecords.filter(r => r.result === 'draw').length;
    const winRate = totalGames > 0 ? Math.round((wins / totalGames) * 100) : 0;

    // 更新统计数据
    const elements = {
      'stats-total-games': totalGames,
      'stats-wins': wins,
      'stats-losses': losses,
      'stats-draws': draws,
      'stats-win-rate': `${winRate}%`
    };

    Object.entries(elements).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element) element.textContent = value;
    });
  }

  // 渲染游戏类型统计
  renderGameTypeStats() {
    const gameTypeStats = {};
    this.gameRecords.forEach(record => {
      if (!gameTypeStats[record.gameType]) {
        gameTypeStats[record.gameType] = { total: 0, wins: 0 };
      }
      gameTypeStats[record.gameType].total++;
      if (record.result === 'win') {
        gameTypeStats[record.gameType].wins++;
      }
    });

    const container = document.getElementById('game-type-stats');
    if (!container) return;

    container.innerHTML = Object.entries(gameTypeStats).map(([gameType, stats]) => {
      const winRate = Math.round((stats.wins / stats.total) * 100);
      return `
        <div class="stat-item">
          <div class="stat-item-header">
            <span class="stat-item-title">${gameType}</span>
            <span class="stat-item-value">${winRate}%</span>
          </div>
          <div class="stat-item-subtitle">${stats.total}局 • ${stats.wins}胜</div>
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${winRate}%"></div>
          </div>
        </div>
      `;
    }).join('');
  }

  // 数据加载方法
  loadGameRecords() {
    const stored = localStorage.getItem('chess-notebook-records');
    if (stored) {
      return JSON.parse(stored);
    }
    
    // 示例数据
    return [
      {
        id: '1',
        gameType: '象棋',
        date: '2024-01-15T14:30:00',
        opponent: '张三',
        result: 'win',
        duration: '45分钟',
        notes: '开局顺利，中局控制良好'
      },
      {
        id: '2',
        gameType: '围棋',
        date: '2024-01-14T19:20:00',
        opponent: '李四',
        result: 'loss',
        duration: '1小时20分钟',
        notes: '布局阶段失误较多，需要加强'
      },
      {
        id: '3',
        gameType: '国际象棋',
        date: '2024-01-13T16:45:00',
        opponent: '王五',
        result: 'draw',
        duration: '55分钟',
        notes: '残局阶段势均力敌'
      }
    ];
  }

  loadOpponents() {
    const stored = localStorage.getItem('chess-notebook-opponents');
    if (stored) {
      return JSON.parse(stored);
    }
    
    return [
      { id: '1', name: '张三', level: '业余5段', totalGames: 8, wins: 5 },
      { id: '2', name: '李四', level: '业余3段', totalGames: 12, wins: 7 },
      { id: '3', name: '王五', level: '业余4段', totalGames: 6, wins: 3 }
    ];
  }

  loadKnowledge() {
    const stored = localStorage.getItem('chess-notebook-knowledge');
    if (stored) {
      return JSON.parse(stored);
    }
    
    return [
      {
        id: '1',
        title: '象棋开局要点',
        category: '开局',
        gameType: '象棋',
        content: '开局阶段要注意子力发展和阵型协调...'
      },
      {
        id: '2',
        title: '围棋布局原理',
        category: '布局',
        gameType: '围棋',
        content: '布局阶段要重视角部和边部的价值...'
      }
    ];
  }

  loadTags() {
    return ['重要对局', '练习赛', '比赛', '在线对局', '线下对局'];
  }

  // 工具方法
  formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getResultText(result) {
    const resultMap = {
      'win': '胜',
      'loss': '负',
      'draw': '和'
    };
    return resultMap[result] || result;
  }

  // 保存数据
  saveGameRecords() {
    localStorage.setItem('chess-notebook-records', JSON.stringify(this.gameRecords));
  }

  saveOpponents() {
    localStorage.setItem('chess-notebook-opponents', JSON.stringify(this.opponents));
  }

  saveKnowledge() {
    localStorage.setItem('chess-notebook-knowledge', JSON.stringify(this.knowledge));
  }

  // 返回上一页
  goBack() {
    // 简单的返回逻辑，实际应用中可以维护页面栈
    this.showPage('home');
  }
}

// 应用初始化
document.addEventListener('DOMContentLoaded', () => {
  window.app = new ChessNotebookApp();
});

// 触摸事件优化
document.addEventListener('touchstart', function() {}, { passive: true });
document.addEventListener('touchmove', function() {}, { passive: true });
