<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对手详情</title>
    <link rel="stylesheet" href="ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .opponent-header {
            background: linear-gradient(135deg, var(--ios-blue), var(--ios-purple));
            padding: var(--spacing-lg) var(--spacing-md);
            color: white;
            text-align: center;
        }

        .opponent-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-md);
            font-size: 32px;
            font-weight: 600;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .opponent-name {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: var(--spacing-xs);
        }

        .opponent-level {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: var(--spacing-md);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .info-section {
            background: white;
            margin: var(--spacing-md);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .info-item {
            margin-bottom: var(--spacing-md);
        }

        .info-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }

        .info-content {
            font-size: 16px;
            color: var(--text-primary);
            line-height: 1.5;
        }

        .games-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .game-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md);
            border-bottom: 0.5px solid var(--ios-gray5);
            cursor: pointer;
            transition: background 0.2s;
        }

        .game-item:hover {
            background: var(--ios-gray6);
        }

        .game-item:last-child {
            border-bottom: none;
        }

        .game-info {
            flex: 1;
        }

        .game-type {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .game-date {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .game-result {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            color: white;
        }

        .game-result.win { background: var(--ios-green); }
        .game-result.lose { background: var(--ios-red); }
        .game-result.draw { background: var(--ios-orange); }

        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
        }

        .action-button {
            flex: 1;
            padding: var(--spacing-md);
            border-radius: var(--radius-lg);
            font-size: 16px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-button.primary {
            background: var(--ios-blue);
            color: white;
        }

        .action-button.secondary {
            background: var(--ios-gray6);
            color: var(--text-primary);
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .win-rate-chart {
            height: 120px;
            background: var(--ios-gray6);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: var(--spacing-md) 0;
            position: relative;
            overflow: hidden;
        }

        .win-rate-bar {
            height: 8px;
            background: var(--ios-gray4);
            border-radius: 4px;
            width: 80%;
            position: relative;
        }

        .win-rate-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--ios-red), var(--ios-orange), var(--ios-green));
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .win-rate-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .strength-weakness {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }

        .strength-item, .weakness-item {
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            text-align: center;
        }

        .strength-item {
            background: rgba(52, 199, 89, 0.1);
            border: 1px solid var(--ios-green);
        }

        .weakness-item {
            background: rgba(255, 59, 48, 0.1);
            border: 1px solid var(--ios-red);
        }

        .strength-icon, .weakness-icon {
            font-size: 24px;
            margin-bottom: var(--spacing-sm);
        }

        .strength-icon {
            color: var(--ios-green);
        }

        .weakness-icon {
            color: var(--ios-red);
        }

        .empty-games {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--text-secondary);
        }

        .empty-games i {
            font-size: 48px;
            margin-bottom: var(--spacing-md);
            opacity: 0.3;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                    </div>
                    <div class="wifi-icon"></div>
                    <span>100%</span>
                    <div class="battery-icon"></div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <button class="nav-button" onclick="Navigator.back()">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">对手详情</div>
                <button class="nav-button" onclick="showMoreOptions()">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>

            <!-- 对手信息头部 -->
            <div class="opponent-header">
                <div class="opponent-avatar" id="opponentAvatar">对</div>
                <div class="opponent-name" id="opponentName">对手姓名</div>
                <div class="opponent-level" id="opponentLevel">水平等级</div>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="totalGames">0</div>
                        <div class="stat-label">总对局</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="wins">0</div>
                        <div class="stat-label">胜利</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="losses">0</div>
                        <div class="stat-label">失败</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="winRate">0%</div>
                        <div class="stat-label">胜率</div>
                    </div>
                </div>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <!-- 胜率图表 -->
                <div class="info-section">
                    <div class="section-title">
                        <i class="fas fa-chart-line"></i>
                        胜率分析
                    </div>
                    <div class="win-rate-chart">
                        <div class="win-rate-bar">
                            <div class="win-rate-fill" id="winRateFill"></div>
                        </div>
                        <div class="win-rate-text" id="winRateText">0%</div>
                    </div>
                </div>

                <!-- 对手信息 -->
                <div class="info-section">
                    <div class="section-title">
                        <i class="fas fa-user"></i>
                        基本信息
                    </div>
                    <div class="info-item">
                        <div class="info-label">棋风特点</div>
                        <div class="info-content" id="opponentStyle">待分析</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">备注</div>
                        <div class="info-content" id="opponentNotes">暂无备注</div>
                    </div>
                </div>

                <!-- 优势劣势 -->
                <div class="info-section">
                    <div class="section-title">
                        <i class="fas fa-balance-scale"></i>
                        优势劣势
                    </div>
                    <div class="strength-weakness">
                        <div class="strength-item">
                            <div class="strength-icon">
                                <i class="fas fa-thumbs-up"></i>
                            </div>
                            <div class="info-label">优势</div>
                            <div class="info-content" id="strengths">待分析</div>
                        </div>
                        <div class="weakness-item">
                            <div class="weakness-icon">
                                <i class="fas fa-thumbs-down"></i>
                            </div>
                            <div class="info-label">劣势</div>
                            <div class="info-content" id="weaknesses">待分析</div>
                        </div>
                    </div>
                </div>

                <!-- 对局历史 -->
                <div class="info-section">
                    <div class="section-title">
                        <i class="fas fa-history"></i>
                        对局历史
                    </div>
                    <div class="games-list" id="gamesList">
                        <!-- 动态加载对局列表 -->
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="action-button secondary" onclick="editOpponent()">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="action-button primary" onclick="addGameWithOpponent()">
                        <i class="fas fa-plus"></i> 新增对局
                    </button>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="app-utils.js"></script>
    <script>
        let currentOpponent = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            const params = Navigator.getParams();
            if (params.id) {
                loadOpponentDetail(params.id);
            } else {
                UIUtils.showToast('对手信息不存在', 'error');
                Navigator.back();
            }
        });

        // 加载对手详情
        function loadOpponentDetail(opponentId) {
            const opponents = appStorage.get('opponents') || [];
            currentOpponent = opponents.find(o => o.id === opponentId);

            if (!currentOpponent) {
                UIUtils.showToast('对手信息不存在', 'error');
                Navigator.back();
                return;
            }

            // 更新基本信息
            document.getElementById('opponentAvatar').textContent = currentOpponent.avatar;
            document.getElementById('opponentName').textContent = currentOpponent.name;
            document.getElementById('opponentLevel').textContent = currentOpponent.level;
            document.getElementById('opponentStyle').textContent = currentOpponent.style || '待分析';
            document.getElementById('opponentNotes').textContent = currentOpponent.notes || '暂无备注';
            document.getElementById('strengths').textContent = currentOpponent.strengths || '待分析';
            document.getElementById('weaknesses').textContent = currentOpponent.weaknesses || '待分析';

            // 更新统计数据
            document.getElementById('totalGames').textContent = currentOpponent.totalGames;
            document.getElementById('wins').textContent = currentOpponent.wins;
            document.getElementById('losses').textContent = currentOpponent.losses;

            const winRate = currentOpponent.totalGames > 0 ?
                (currentOpponent.wins / currentOpponent.totalGames * 100) : 0;
            document.getElementById('winRate').textContent = winRate.toFixed(1) + '%';
            document.getElementById('winRateText').textContent = winRate.toFixed(1) + '%';

            // 更新胜率图表
            const winRateFill = document.getElementById('winRateFill');
            winRateFill.style.width = winRate + '%';

            // 加载对局历史
            loadGameHistory();
        }

        // 加载对局历史
        function loadGameHistory() {
            const records = appStorage.get('gameRecords') || [];
            const opponentGames = records
                .filter(record => record.opponent === currentOpponent.name)
                .sort((a, b) => new Date(b.date) - new Date(a.date));

            const gamesList = document.getElementById('gamesList');

            if (opponentGames.length === 0) {
                gamesList.innerHTML = `
                    <div class="empty-games">
                        <i class="fas fa-chess"></i>
                        <div>暂无对局记录</div>
                        <div style="font-size: 12px; margin-top: 8px;">
                            点击下方按钮开始记录对局
                        </div>
                    </div>
                `;
                return;
            }

            gamesList.innerHTML = opponentGames.map(game => `
                <div class="game-item" onclick="viewGameDetail('${game.id}')">
                    <div class="game-info">
                        <div class="game-type">${game.gameType}</div>
                        <div class="game-date">${DateUtils.formatDate(game.date)} · ${DateUtils.getRelativeTime(game.date)}</div>
                    </div>
                    <div class="game-result ${game.result === '胜' ? 'win' : game.result === '负' ? 'lose' : 'draw'}">
                        ${game.result}
                    </div>
                </div>
            `).join('');
        }

        // 查看对局详情
        function viewGameDetail(gameId) {
            Navigator.navigate('record-detail.html', { id: gameId });
        }

        // 编辑对手信息
        function editOpponent() {
            Navigator.navigate('add-opponent.html', { id: currentOpponent.id });
        }

        // 与该对手新增对局
        function addGameWithOpponent() {
            Navigator.navigate('add-record.html', { opponent: currentOpponent.name });
        }

        // 显示更多选项
        function showMoreOptions() {
            const options = [
                {
                    text: '编辑对手信息',
                    icon: 'fas fa-edit',
                    action: editOpponent
                },
                {
                    text: '查看所有对局',
                    icon: 'fas fa-list',
                    action: () => Navigator.navigate('records.html', { opponent: currentOpponent.name })
                },
                {
                    text: '删除对手',
                    icon: 'fas fa-trash',
                    action: deleteOpponent,
                    destructive: true
                }
            ];

            UIUtils.showActionSheet('对手操作', options);
        }

        // 删除对手
        function deleteOpponent() {
            UIUtils.showDialog(
                '删除对手',
                `确定要删除对手"${currentOpponent.name}"吗？\n\n删除后对局记录仍会保留，但对手信息将被清除。`,
                [
                    { text: '取消', action: () => {} },
                    {
                        text: '删除',
                        action: () => {
                            const opponents = appStorage.get('opponents') || [];
                            const updatedOpponents = opponents.filter(o => o.id !== currentOpponent.id);
                            appStorage.set('opponents', updatedOpponents);

                            UIUtils.showToast('对手已删除', 'success');
                            Navigator.back();
                        },
                        destructive: true
                    }
                ]
            );
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                Navigator.back();
            } else if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
                e.preventDefault();
                editOpponent();
            } else if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                addGameWithOpponent();
            }
        });
    </script>
</body>
</html>
