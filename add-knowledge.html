<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增技巧</title>
    <link rel="stylesheet" href="ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .form-section {
            background: white;
            margin: var(--spacing-md);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-label {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            border: 1px solid var(--ios-gray4);
            border-radius: var(--radius-md);
            font-size: 16px;
            background: white;
            transition: all 0.2s;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--ios-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
            font-family: inherit;
        }

        .category-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: var(--spacing-sm);
        }

        .category-option {
            padding: var(--spacing-md);
            border: 1px solid var(--ios-gray4);
            border-radius: var(--radius-md);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
            font-weight: 500;
        }

        .category-option:hover {
            border-color: var(--ios-blue);
        }

        .category-option.selected {
            background: var(--ios-blue);
            color: white;
            border-color: var(--ios-blue);
        }

        .game-type-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: var(--spacing-sm);
        }

        .game-type-option {
            padding: var(--spacing-md);
            border: 1px solid var(--ios-gray4);
            border-radius: var(--radius-md);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .game-type-option:hover {
            border-color: var(--ios-blue);
        }

        .game-type-option.selected {
            background: var(--ios-blue);
            color: white;
            border-color: var(--ios-blue);
        }

        .game-type-icon {
            font-size: 24px;
        }

        .game-type-name {
            font-size: 12px;
            font-weight: 500;
        }

        .difficulty-selector {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .difficulty-stars {
            display: flex;
            gap: var(--spacing-xs);
        }

        .difficulty-star {
            font-size: 24px;
            color: var(--ios-gray4);
            cursor: pointer;
            transition: color 0.2s;
        }

        .difficulty-star.active {
            color: var(--secondary-color);
        }

        .difficulty-star:hover {
            color: var(--secondary-color);
        }

        .tag-input-container {
            position: relative;
        }

        .tag-input {
            width: 100%;
            padding: var(--spacing-md);
            border: 1px solid var(--ios-gray4);
            border-radius: var(--radius-md);
            font-size: 16px;
        }

        .tag-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid var(--ios-gray4);
            border-top: none;
            border-radius: 0 0 var(--radius-md) var(--radius-md);
            max-height: 200px;
            overflow-y: auto;
            z-index: 10;
            display: none;
        }

        .tag-suggestion {
            padding: var(--spacing-sm) var(--spacing-md);
            cursor: pointer;
            border-bottom: 0.5px solid var(--ios-gray5);
        }

        .tag-suggestion:hover {
            background: var(--ios-gray6);
        }

        .tag-suggestion:last-child {
            border-bottom: none;
        }

        .selected-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
        }

        .tag-chip {
            background: var(--ios-blue);
            color: white;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .tag-chip .remove {
            cursor: pointer;
            font-size: 10px;
        }

        .save-button {
            width: 100%;
            background: var(--ios-blue);
            color: white;
            border: none;
            padding: var(--spacing-lg);
            border-radius: var(--radius-lg);
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            margin-top: var(--spacing-lg);
        }

        .save-button:hover {
            background: var(--ios-blue-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .save-button:disabled {
            background: var(--ios-gray);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .nav-buttons {
            display: flex;
            gap: var(--spacing-sm);
        }

        .nav-button.secondary {
            background: transparent;
            color: var(--ios-blue);
            border: 1px solid var(--ios-blue);
            padding: 6px 8px;
            border-radius: 6px;
        }

        .nav-button.secondary:hover {
            background: var(--ios-blue);
            color: white;
        }

        .form-input.error {
            border-color: var(--ios-red);
            background: #fff5f5;
        }

        .form-group.error .form-label {
            color: var(--ios-red);
        }

        .char-counter {
            font-size: 12px;
            color: var(--ios-gray);
            text-align: right;
            margin-top: 4px;
        }

        .char-counter.over-limit {
            color: var(--ios-red);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                    </div>
                    <div class="wifi-icon"></div>
                    <span>100%</span>
                    <div class="battery-icon"></div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <button class="nav-button" onclick="Navigator.back()">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">新增技巧</div>
                <div class="nav-buttons">
                    <button class="nav-button secondary" onclick="resetForm()" id="resetButton">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button class="nav-button" onclick="saveKnowledge()" id="navSaveButton">保存</button>
                </div>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <form id="knowledgeForm">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">技巧标题 *</label>
                            <input type="text" class="form-input" id="title" placeholder="请输入技巧标题" maxlength="100">
                            <div class="char-counter" id="titleCounter">0/100</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">分类 *</label>
                            <div class="category-options" id="categoryOptions">
                                <div class="category-option" data-category="开局">开局</div>
                                <div class="category-option" data-category="中局">中局</div>
                                <div class="category-option" data-category="残局">残局</div>
                                <div class="category-option" data-category="技巧">技巧</div>
                                <div class="category-option" data-category="战术">战术</div>
                                <div class="category-option" data-category="心理">心理</div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">游戏类型 *</label>
                            <div class="game-type-options" id="gameTypeOptions">
                                <!-- 动态生成 -->
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">难度等级</label>
                            <div class="difficulty-selector">
                                <span>简单</span>
                                <div class="difficulty-stars" id="difficultyStars">
                                    <span class="difficulty-star" data-rating="1">⭐</span>
                                    <span class="difficulty-star" data-rating="2">⭐</span>
                                    <span class="difficulty-star" data-rating="3">⭐</span>
                                    <span class="difficulty-star" data-rating="4">⭐</span>
                                    <span class="difficulty-star" data-rating="5">⭐</span>
                                </div>
                                <span>困难</span>
                            </div>
                        </div>
                    </div>

                    <!-- 内容 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">详细内容 *</label>
                            <textarea class="form-input form-textarea" id="content" placeholder="请详细描述这个技巧的内容、应用场景、注意事项等..." maxlength="2000"></textarea>
                            <div class="char-counter" id="contentCounter">0/2000</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">标签</label>
                            <div class="tag-input-container">
                                <input type="text" class="tag-input" id="tagInput" placeholder="输入标签并按回车添加">
                                <div class="tag-suggestions" id="tagSuggestions"></div>
                            </div>
                            <div class="selected-tags" id="selectedTags"></div>
                        </div>
                    </div>

                    <!-- 保存按钮 -->
                    <div style="padding: 0 var(--spacing-md) var(--spacing-md);">
                        <button type="button" class="save-button" onclick="saveKnowledge()">
                            <i class="fas fa-save"></i> 保存技巧
                        </button>
                    </div>
                </form>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="app-utils.js"></script>
    <script>
        let selectedCategory = '';
        let selectedGameType = '';
        let selectedDifficulty = 1;
        let selectedTags = [];
        let draftTimer = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initGameTypes();
            initCategoryOptions();
            initDifficultyStars();
            initTagInput();
            initFormValidation();

            // 检查是否为编辑模式
            const params = Navigator.getParams();
            if (params.edit === 'true') {
                document.querySelector('.nav-title').textContent = '编辑技巧';
                document.getElementById('resetButton').style.display = 'none';
                loadEditData(params);
            } else {
                loadDraft();
            }
        });

        // 初始化游戏类型选项
        function initGameTypes() {
            const container = document.getElementById('gameTypeOptions');
            container.innerHTML = Object.entries(GAME_TYPES).map(([type, info]) => `
                <div class="game-type-option" data-type="${type}">
                    <div class="game-type-icon">${info.icon}</div>
                    <div class="game-type-name">${type}</div>
                </div>
            `).join('');

            // 添加点击事件
            container.querySelectorAll('.game-type-option').forEach(option => {
                option.addEventListener('click', function() {
                    container.querySelectorAll('.game-type-option').forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedGameType = this.dataset.type;
                    scheduleDraftSave();
                });
            });
        }

        // 初始化分类选项
        function initCategoryOptions() {
            const container = document.getElementById('categoryOptions');
            container.querySelectorAll('.category-option').forEach(option => {
                option.addEventListener('click', function() {
                    container.querySelectorAll('.category-option').forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedCategory = this.dataset.category;
                    scheduleDraftSave();
                });
            });
        }

        // 初始化难度星级
        function initDifficultyStars() {
            const stars = document.querySelectorAll('.difficulty-star');
            stars.forEach((star, index) => {
                star.addEventListener('click', function() {
                    selectedDifficulty = parseInt(this.dataset.rating);
                    updateDifficultyStars();
                    scheduleDraftSave();
                });
            });
            updateDifficultyStars();
        }

        // 更新难度星级显示
        function updateDifficultyStars() {
            const stars = document.querySelectorAll('.difficulty-star');
            stars.forEach((star, index) => {
                star.classList.toggle('active', index < selectedDifficulty);
            });
        }

        // 初始化标签输入
        function initTagInput() {
            const tagInput = document.getElementById('tagInput');
            const suggestions = document.getElementById('tagSuggestions');

            tagInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    addTag(this.value.trim());
                    this.value = '';
                    suggestions.style.display = 'none';
                }
            });

            tagInput.addEventListener('input', function() {
                const value = this.value.trim();
                if (value.length > 0) {
                    showTagSuggestions(value);
                } else {
                    suggestions.style.display = 'none';
                }
                scheduleDraftSave();
            });

            tagInput.addEventListener('focus', function() {
                if (this.value.trim().length > 0) {
                    showTagSuggestions(this.value.trim());
                }
            });

            document.addEventListener('click', function(e) {
                if (!tagInput.contains(e.target) && !suggestions.contains(e.target)) {
                    suggestions.style.display = 'none';
                }
            });
        }

        // 显示标签建议
        function showTagSuggestions(input) {
            const suggestions = document.getElementById('tagSuggestions');
            const allTags = getAllExistingTags();

            const matchedTags = allTags.filter(tag =>
                tag.toLowerCase().includes(input.toLowerCase()) &&
                !selectedTags.includes(tag)
            );

            if (matchedTags.length > 0) {
                suggestions.innerHTML = matchedTags.map(tag =>
                    `<div class="tag-suggestion" onclick="selectSuggestion('${tag}')">${tag}</div>`
                ).join('');
                suggestions.style.display = 'block';
            } else {
                suggestions.style.display = 'none';
            }
        }

        // 获取所有已存在的标签
        function getAllExistingTags() {
            const knowledge = appStorage.get('knowledge') || [];
            const tagSet = new Set();

            knowledge.forEach(item => {
                if (item.tags && Array.isArray(item.tags)) {
                    item.tags.forEach(tag => tagSet.add(tag));
                }
            });

            const commonTags = ['基础', '进阶', '高级', '实战', '理论', '技巧', '战术', '心理', '开局', '中局', '残局'];
            commonTags.forEach(tag => tagSet.add(tag));

            return Array.from(tagSet).sort();
        }

        // 选择建议标签
        function selectSuggestion(tag) {
            addTag(tag);
            document.getElementById('tagInput').value = '';
            document.getElementById('tagSuggestions').style.display = 'none';
        }

        // 添加标签
        function addTag(tag) {
            if (tag && !selectedTags.includes(tag) && selectedTags.length < 10) {
                selectedTags.push(tag);
                updateTagsDisplay();
                scheduleDraftSave();
            }
        }

        // 移除标签
        function removeTag(tag) {
            selectedTags = selectedTags.filter(t => t !== tag);
            updateTagsDisplay();
            scheduleDraftSave();
        }

        // 更新标签显示
        function updateTagsDisplay() {
            const container = document.getElementById('selectedTags');
            container.innerHTML = selectedTags.map(tag => `
                <div class="tag-chip">
                    <span>${tag}</span>
                    <span class="remove" onclick="removeTag('${tag}')">×</span>
                </div>
            `).join('');
        }

        // 初始化表单验证
        function initFormValidation() {
            const titleInput = document.getElementById('title');
            const contentInput = document.getElementById('content');

            titleInput.addEventListener('input', function() {
                updateCharCounter('title', this.value.length, 100);
                validateField(this, this.value.trim().length > 0 && this.value.trim().length <= 100);
                scheduleDraftSave();
            });

            contentInput.addEventListener('input', function() {
                updateCharCounter('content', this.value.length, 2000);
                validateField(this, this.value.trim().length > 0 && this.value.trim().length <= 2000);
                scheduleDraftSave();
            });
        }

        // 更新字符计数
        function updateCharCounter(fieldId, current, max) {
            const counter = document.getElementById(fieldId + 'Counter');
            counter.textContent = `${current}/${max}`;
            counter.classList.toggle('over-limit', current > max);
        }

        // 验证单个字段
        function validateField(input, isValid) {
            const formGroup = input.closest('.form-group');
            if (isValid) {
                input.classList.remove('error');
                formGroup.classList.remove('error');
            } else {
                input.classList.add('error');
                formGroup.classList.add('error');
            }
        }

        // 表单验证
        function validateForm() {
            const errors = [];

            const title = document.getElementById('title').value.trim();
            if (!title) {
                errors.push('请输入技巧标题');
            } else if (title.length > 100) {
                errors.push('技巧标题不能超过100个字符');
            }

            if (!selectedCategory) {
                errors.push('请选择分类');
            }

            if (!selectedGameType) {
                errors.push('请选择游戏类型');
            }

            const content = document.getElementById('content').value.trim();
            if (!content) {
                errors.push('请输入详细内容');
            } else if (content.length > 2000) {
                errors.push('详细内容不能超过2000个字符');
            }

            if (selectedTags.length > 10) {
                errors.push('标签数量不能超过10个');
            }

            return errors;
        }

        // 保存技巧
        function saveKnowledge() {
            const errors = validateForm();
            if (errors.length > 0) {
                UIUtils.showToast(errors[0], 'error');
                return;
            }

            const saveButton = document.querySelector('.save-button');
            const navSaveButton = document.getElementById('navSaveButton');
            saveButton.disabled = true;
            if (navSaveButton) navSaveButton.disabled = true;

            const params = Navigator.getParams();
            const isEdit = params.edit === 'true';

            const knowledge = {
                title: document.getElementById('title').value.trim(),
                category: selectedCategory,
                gameType: selectedGameType,
                content: document.getElementById('content').value.trim(),
                difficulty: selectedDifficulty,
                tags: [...selectedTags],
                isFavorite: false
            };

            try {
                if (isEdit && params.id) {
                    knowledge.id = params.id;
                    knowledge.updatedAt = new Date().toISOString();

                    const knowledgeList = appStorage.get('knowledge') || [];
                    const index = knowledgeList.findIndex(k => k.id === params.id);

                    if (index !== -1) {
                        // 保留原有的创建时间和收藏状态
                        knowledge.createdAt = knowledgeList[index].createdAt;
                        knowledge.isFavorite = knowledgeList[index].isFavorite;
                        knowledgeList[index] = knowledge;
                        appStorage.set('knowledge', knowledgeList);

                        UIUtils.showToast('技巧更新成功', 'success');
                        setTimeout(() => {
                            Navigator.navigate('knowledge-detail.html', { id: params.id });
                        }, 1000);
                    } else {
                        throw new Error('技巧不存在');
                    }
                } else {
                    knowledge.id = Date.now().toString();
                    knowledge.createdAt = new Date().toISOString();

                    const knowledgeList = appStorage.get('knowledge') || [];
                    knowledgeList.unshift(knowledge);
                    appStorage.set('knowledge', knowledgeList);

                    clearDraft();
                    UIUtils.showToast('技巧保存成功', 'success');
                    setTimeout(() => {
                        Navigator.navigate('knowledge.html');
                    }, 1000);
                }
            } catch (error) {
                console.error('保存技巧失败:', error);
                UIUtils.showToast('保存失败，请重试', 'error');
                saveButton.disabled = false;
                if (navSaveButton) navSaveButton.disabled = false;
            }
        }

        // 重置表单
        function resetForm() {
            UIUtils.showDialog('确认重置', '确定要重置表单吗？所有输入的内容将被清空。', [
                {
                    text: '取消',
                    action: () => {}
                },
                {
                    text: '确定',
                    action: () => {
                        selectedCategory = '';
                        selectedGameType = '';
                        selectedDifficulty = 1;
                        selectedTags = [];

                        document.getElementById('knowledgeForm').reset();

                        document.querySelectorAll('.category-option').forEach(opt => opt.classList.remove('selected'));
                        document.querySelectorAll('.game-type-option').forEach(opt => opt.classList.remove('selected'));

                        updateDifficultyStars();
                        updateTagsDisplay();
                        updateCharCounter('title', 0, 100);
                        updateCharCounter('content', 0, 2000);

                        clearDraft();
                        UIUtils.showToast('表单已重置', 'info');
                    }
                }
            ]);
        }

        // 加载编辑数据
        function loadEditData(params) {
            if (!params.id) return;

            const knowledgeList = appStorage.get('knowledge') || [];
            const knowledge = knowledgeList.find(k => k.id === params.id);

            if (!knowledge) {
                UIUtils.showToast('技巧不存在', 'error');
                Navigator.back();
                return;
            }

            selectedCategory = knowledge.category;
            selectedGameType = knowledge.gameType;
            selectedDifficulty = knowledge.difficulty || 1;
            selectedTags = knowledge.tags || [];

            document.getElementById('title').value = knowledge.title;
            document.getElementById('content').value = knowledge.content;

            document.querySelectorAll('.category-option').forEach(option => {
                option.classList.toggle('selected', option.dataset.category === selectedCategory);
            });

            document.querySelectorAll('.game-type-option').forEach(option => {
                option.classList.toggle('selected', option.dataset.type === selectedGameType);
            });

            updateDifficultyStars();
            updateTagsDisplay();
            updateCharCounter('title', knowledge.title.length, 100);
            updateCharCounter('content', knowledge.content.length, 2000);
        }

        // 草稿保存相关功能
        function scheduleDraftSave() {
            const params = Navigator.getParams();
            if (params.edit === 'true') return;

            if (draftTimer) {
                clearTimeout(draftTimer);
            }
            draftTimer = setTimeout(saveDraft, 2000);
        }

        function saveDraft() {
            const params = Navigator.getParams();
            if (params.edit === 'true') return;

            const draft = {
                title: document.getElementById('title').value.trim(),
                category: selectedCategory,
                gameType: selectedGameType,
                content: document.getElementById('content').value.trim(),
                difficulty: selectedDifficulty,
                tags: [...selectedTags],
                timestamp: Date.now()
            };

            const hasContent = draft.title || draft.content || draft.category || draft.gameType || draft.tags.length > 0;

            if (hasContent) {
                localStorage.setItem('knowledgeDraft', JSON.stringify(draft));
            }
        }

        function loadDraft() {
            const draftStr = localStorage.getItem('knowledgeDraft');
            if (!draftStr) return;

            try {
                const draft = JSON.parse(draftStr);

                if (Date.now() - draft.timestamp > 24 * 60 * 60 * 1000) {
                    localStorage.removeItem('knowledgeDraft');
                    return;
                }

                UIUtils.showDialog('发现草稿', '检测到未完成的技巧草稿，是否恢复？', [
                    {
                        text: '忽略',
                        action: () => {
                            localStorage.removeItem('knowledgeDraft');
                        }
                    },
                    {
                        text: '恢复',
                        action: () => {
                            restoreDraft(draft);
                            localStorage.removeItem('knowledgeDraft');
                        }
                    }
                ]);
            } catch (error) {
                console.error('加载草稿失败:', error);
                localStorage.removeItem('knowledgeDraft');
            }
        }

        function restoreDraft(draft) {
            selectedCategory = draft.category || '';
            selectedGameType = draft.gameType || '';
            selectedDifficulty = draft.difficulty || 1;
            selectedTags = draft.tags || [];

            document.getElementById('title').value = draft.title || '';
            document.getElementById('content').value = draft.content || '';

            document.querySelectorAll('.category-option').forEach(option => {
                option.classList.toggle('selected', option.dataset.category === selectedCategory);
            });

            document.querySelectorAll('.game-type-option').forEach(option => {
                option.classList.toggle('selected', option.dataset.type === selectedGameType);
            });

            updateDifficultyStars();
            updateTagsDisplay();
            updateCharCounter('title', draft.title?.length || 0, 100);
            updateCharCounter('content', draft.content?.length || 0, 2000);

            UIUtils.showToast('草稿已恢复', 'success');
        }

        function clearDraft() {
            localStorage.removeItem('knowledgeDraft');
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                saveKnowledge();
            } else if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                const params = Navigator.getParams();
                if (params.edit !== 'true') {
                    e.preventDefault();
                    resetForm();
                }
            } else if (e.key === 'Escape') {
                Navigator.back();
            }
        });
    </script>
</body>
</html>
