/* iOS 原型基础框架样式 */
:root {
  /* iOS 设计系统颜色 */
  --ios-blue: #007AFF;
  --ios-green: #34C759;
  --ios-orange: #FF9500;
  --ios-red: #FF3B30;
  --ios-purple: #AF52DE;
  --ios-pink: #FF2D92;
  --ios-yellow: #FFCC00;
  --ios-gray: #8E8E93;
  --ios-gray2: #AEAEB2;
  --ios-gray3: #C7C7CC;
  --ios-gray4: #D1D1D6;
  --ios-gray5: #E5E5EA;
  --ios-gray6: #F2F2F7;
  
  /* 应用主题色 */
  --primary-color: #1E3A8A;
  --secondary-color: #F59E0B;
  --background-color: #F8FAFC;
  --text-primary: #374151;
  --text-secondary: #6B7280;
  --text-tertiary: #9CA3AF;
  
  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 圆角 */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* iPhone 15 Pro 容器 */
.iphone-container {
  width: 393px;
  height: 852px;
  margin: 20px auto;
  background: #000;
  border-radius: 47px;
  padding: 2px;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
  position: relative;
}

.iphone-screen {
  width: 100%;
  height: 100%;
  background: var(--background-color);
  border-radius: 45px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 状态栏 */
.status-bar {
  height: 47px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--spacing-md);
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
  z-index: 100;
}

.status-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.status-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.signal-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.signal-bar {
  width: 3px;
  background: var(--text-primary);
  border-radius: 1px;
}

.signal-bar:nth-child(1) { height: 4px; }
.signal-bar:nth-child(2) { height: 6px; }
.signal-bar:nth-child(3) { height: 8px; }
.signal-bar:nth-child(4) { height: 10px; }

.wifi-icon, .battery-icon {
  width: 15px;
  height: 11px;
  background: var(--text-primary);
  border-radius: 2px;
  position: relative;
}

.battery-icon::after {
  content: '';
  position: absolute;
  right: -2px;
  top: 3px;
  width: 1px;
  height: 5px;
  background: var(--text-primary);
  border-radius: 0 1px 1px 0;
}

/* 导航栏 */
.nav-bar {
  height: 44px;
  background: var(--background-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
  border-bottom: 0.5px solid var(--ios-gray5);
  position: relative;
  z-index: 99;
}

.nav-title {
  font-size: 17px;
  font-weight: 600;
  color: var(--text-primary);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.nav-button {
  background: none;
  border: none;
  color: var(--ios-blue);
  font-size: 17px;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: background-color 0.2s;
}

.nav-button:hover {
  background-color: var(--ios-gray6);
}

.nav-button.disabled {
  color: var(--ios-gray);
  cursor: not-allowed;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  overflow-y: auto;
  background: var(--background-color);
}

/* 底部标签栏 */
.tab-bar {
  height: 83px;
  background: var(--background-color);
  border-top: 0.5px solid var(--ios-gray5);
  display: flex;
  align-items: flex-start;
  padding-top: var(--spacing-sm);
  position: relative;
  z-index: 99;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
  transition: color 0.2s;
  text-decoration: none;
  color: var(--ios-gray);
}

.tab-item.active {
  color: var(--ios-blue);
}

.tab-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-label {
  font-size: 10px;
  font-weight: 500;
}

/* Home Indicator */
.home-indicator {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 134px;
  height: 5px;
  background: var(--text-primary);
  border-radius: 3px;
  opacity: 0.3;
}

/* 通用组件样式 */
.card {
  background: white;
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin: var(--spacing-sm) var(--spacing-md);
  box-shadow: var(--shadow-sm);
  border: 0.5px solid var(--ios-gray5);
}

.list-item {
  background: white;
  padding: var(--spacing-md);
  border-bottom: 0.5px solid var(--ios-gray5);
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.list-item:hover {
  background-color: var(--ios-gray6);
}

.list-item:last-child {
  border-bottom: none;
}

.button-primary {
  background: var(--ios-blue);
  color: white;
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-size: 17px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.button-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.button-primary:hover::before {
  left: 100%;
}

.button-primary:hover {
  background: #0056CC;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
}

.button-primary:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s;
}

.button-secondary {
  background: var(--ios-gray6);
  color: var(--text-primary);
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-size: 17px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.button-secondary:hover {
  background: var(--ios-gray5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.button-secondary:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s;
}

/* 输入框样式 */
.input-field {
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid var(--ios-gray4);
  border-radius: var(--radius-md);
  font-size: 17px;
  background: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.input-field:focus {
  outline: none;
  border-color: var(--ios-blue);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
  transform: translateY(-1px);
}

.input-field:hover {
  border-color: var(--ios-gray3);
}

/* 页面过渡动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 动画类 */
.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-bounce {
  animation: bounce 0.6s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* 悬停效果增强 */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
  transition: transform 0.2s ease-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* 涟漪效果 */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
  width: 300px;
  height: 300px;
}

/* Toast动画 */
@keyframes toastSlideIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
}

@keyframes toastSlideOut {
  from {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px) scale(0.9);
  }
}

/* 对话框动画 */
@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 涟漪动画 */
@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* 增强的悬停效果 */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

/* 按钮点击反馈 */
.button-feedback {
  position: relative;
  overflow: hidden;
}

.button-feedback::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.button-feedback:active::after {
  width: 200px;
  height: 200px;
}

/* 页面切换动画 */
.page-transition-enter {
  animation: slideInFromRight 0.3s ease-out;
}

.page-transition-exit {
  animation: slideOutToLeft 0.3s ease-out;
}

@keyframes slideOutToLeft {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-100%);
  }
}

/* 列表项动画 */
.list-item-animate {
  animation: listItemSlideIn 0.3s ease-out;
}

@keyframes listItemSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 标签栏动画 */
.tab-item {
  transition: all 0.2s ease-out;
}

.tab-item:active {
  transform: scale(0.95);
}

.tab-icon {
  transition: transform 0.2s ease-out;
}

.tab-item.active .tab-icon {
  transform: scale(1.1);
}

/* 浮动按钮动画 */
.floating-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-button:hover {
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 8px 25px rgba(0, 122, 255, 0.4);
}

.floating-button:active {
  transform: scale(0.95) rotate(90deg);
}

/* 动画 */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

/* 统计卡片样式 */
.stat-card {
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card:active {
    transform: translateY(0) scale(0.98);
}

.stat-hint {
    font-size: 11px;
    color: var(--text-secondary);
    margin-top: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover .stat-hint {
    opacity: 1;
}

/* 响应式调整 */
@media (max-width: 430px) {
  .iphone-container {
    width: 100%;
    height: 100vh;
    margin: 0;
    border-radius: 0;
    padding: 0;
    box-shadow: none;
  }

  .iphone-screen {
    border-radius: 0;
  }
}
