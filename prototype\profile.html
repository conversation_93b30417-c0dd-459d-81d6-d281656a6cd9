<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>个人中心 - 棋牌记事本</title>
    <link rel="stylesheet" href="css/ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 个人中心特定样式 */
        .profile-header {
            background: linear-gradient(135deg, var(--primary-color), #3B82F6);
            color: white;
            padding: 32px 16px 24px;
            margin: 16px;
            border-radius: var(--border-radius-large);
            text-align: center;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 40px;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin: 0 auto 16px;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }
        
        .profile-name {
            font-size: var(--font-size-title2);
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .profile-level {
            font-size: var(--font-size-subhead);
            opacity: 0.9;
            margin-bottom: 16px;
        }
        
        .profile-stats {
            display: flex;
            justify-content: space-around;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius-medium);
            padding: 16px;
        }
        
        .profile-stat {
            text-align: center;
        }
        
        .profile-stat-number {
            font-size: var(--font-size-title3);
            font-weight: 700;
            margin-bottom: 4px;
        }
        
        .profile-stat-label {
            font-size: var(--font-size-caption1);
            opacity: 0.9;
        }
        
        .menu-section {
            margin: 16px;
        }
        
        .section-title {
            font-size: var(--font-size-title3);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            padding: 0 4px;
        }
        
        .menu-list {
            background: var(--surface-color);
            border-radius: var(--border-radius-medium);
            overflow: hidden;
            box-shadow: var(--shadow-small);
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid var(--border-color);
            cursor: pointer;
            transition: background-color var(--animation-fast);
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .menu-item:hover {
            background-color: var(--ios-gray6);
        }
        
        .menu-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
            color: white;
        }
        
        .menu-icon.blue { background: var(--ios-blue); }
        .menu-icon.green { background: var(--ios-green); }
        .menu-icon.orange { background: var(--ios-orange); }
        .menu-icon.purple { background: var(--ios-purple); }
        .menu-icon.red { background: var(--ios-red); }
        .menu-icon.gray { background: var(--ios-gray); }
        
        .menu-content {
            flex: 1;
        }
        
        .menu-title {
            font-size: var(--font-size-body);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .menu-subtitle {
            font-size: var(--font-size-subhead);
            color: var(--text-secondary);
        }
        
        .menu-chevron {
            color: var(--ios-gray2);
            font-size: 14px;
        }
        
        .menu-badge {
            background: var(--error-color);
            color: white;
            font-size: var(--font-size-caption2);
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            margin-right: 8px;
            min-width: 18px;
            text-align: center;
        }
        
        .toggle-switch {
            position: relative;
            width: 44px;
            height: 26px;
            background: var(--ios-gray3);
            border-radius: 13px;
            cursor: pointer;
            transition: background-color var(--animation-fast);
        }
        
        .toggle-switch.active {
            background: var(--ios-green);
        }
        
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 22px;
            height: 22px;
            background: white;
            border-radius: 11px;
            transition: transform var(--animation-fast);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }
        
        .toggle-switch.active::after {
            transform: translateX(18px);
        }
        
        .version-info {
            text-align: center;
            padding: 24px;
            color: var(--text-tertiary);
            font-size: var(--font-size-caption1);
        }
        
        .achievement-preview {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            justify-content: center;
        }
        
        .achievement-mini {
            width: 24px;
            height: 24px;
            border-radius: 12px;
            background: var(--secondary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }
        
        .achievement-mini.locked {
            background: var(--ios-gray3);
        }
        
        .backup-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .backup-indicator {
            width: 8px;
            height: 8px;
            border-radius: 4px;
            background: var(--success-color);
        }
        
        .backup-indicator.syncing {
            background: var(--warning-color);
            animation: pulse 1.5s infinite;
        }
        
        .backup-indicator.error {
            background: var(--error-color);
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .quick-actions {
            display: flex;
            gap: 12px;
            margin: 16px;
        }
        
        .quick-action-btn {
            flex: 1;
            background: var(--surface-color);
            border: none;
            border-radius: var(--border-radius-medium);
            padding: 16px 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all var(--animation-fast);
            box-shadow: var(--shadow-small);
        }
        
        .quick-action-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }
        
        .quick-action-icon {
            font-size: 20px;
            color: var(--primary-color);
        }
        
        .quick-action-text {
            font-size: var(--font-size-caption1);
            font-weight: 500;
            color: var(--text-primary);
        }
    </style>
</head>
<body>
    <div class="device-container">
        <div class="device-screen">
            <!-- 个人中心页面 -->
            <div id="profile" class="page">
                <!-- 状态栏 -->
                <div class="status-bar">
                    <div class="status-left">
                        <span class="status-time">14:30</span>
                    </div>
                    <div class="status-center">
                        <i class="fas fa-signal" style="font-size: 10px;"></i>
                        <i class="fas fa-wifi" style="font-size: 10px; margin-left: 4px;"></i>
                    </div>
                    <div class="status-right">
                        <i class="fas fa-battery-three-quarters" style="font-size: 12px;"></i>
                        <span style="font-size: 11px; margin-left: 2px;">85%</span>
                    </div>
                </div>

                <!-- 导航栏 -->
                <div class="nav-bar">
                    <button class="nav-button" data-action="back">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="nav-title">个人中心</div>
                    <button class="nav-button" data-action="settings">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>

                <!-- 主内容 -->
                <div class="main-content">
                    <!-- 个人信息头部 -->
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="profile-name">棋牌爱好者</div>
                        <div class="profile-level">业余4段 • 活跃用户</div>
                        <div class="achievement-preview">
                            <div class="achievement-mini">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="achievement-mini">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div class="achievement-mini locked">
                                <i class="fas fa-crown"></i>
                            </div>
                            <div class="achievement-mini locked">
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        <div class="profile-stats">
                            <div class="profile-stat">
                                <div class="profile-stat-number">156</div>
                                <div class="profile-stat-label">总对局</div>
                            </div>
                            <div class="profile-stat">
                                <div class="profile-stat-number">68%</div>
                                <div class="profile-stat-label">胜率</div>
                            </div>
                            <div class="profile-stat">
                                <div class="profile-stat-number">24</div>
                                <div class="profile-stat-label">技巧数</div>
                            </div>
                        </div>
                    </div>

                    <!-- 快捷操作 -->
                    <div class="quick-actions">
                        <button class="quick-action-btn" data-action="export-data">
                            <div class="quick-action-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <div class="quick-action-text">导出数据</div>
                        </button>
                        <button class="quick-action-btn" data-action="import-data">
                            <div class="quick-action-icon">
                                <i class="fas fa-upload"></i>
                            </div>
                            <div class="quick-action-text">导入数据</div>
                        </button>
                        <button class="quick-action-btn" data-action="backup">
                            <div class="quick-action-icon">
                                <i class="fas fa-cloud"></i>
                            </div>
                            <div class="quick-action-text">备份同步</div>
                        </button>
                    </div>

                    <!-- 数据管理 -->
                    <div class="menu-section">
                        <div class="section-title">数据管理</div>
                        <div class="menu-list">
                            <div class="menu-item" data-action="backup-settings">
                                <div class="menu-icon blue">
                                    <i class="fas fa-cloud"></i>
                                </div>
                                <div class="menu-content">
                                    <div class="menu-title">iCloud同步</div>
                                    <div class="backup-status">
                                        <div class="backup-indicator"></div>
                                        <div class="menu-subtitle">已同步 • 2分钟前</div>
                                    </div>
                                </div>
                                <div class="toggle-switch active"></div>
                            </div>
                            <div class="menu-item" data-action="export">
                                <div class="menu-icon green">
                                    <i class="fas fa-download"></i>
                                </div>
                                <div class="menu-content">
                                    <div class="menu-title">导出数据</div>
                                    <div class="menu-subtitle">备份到文件</div>
                                </div>
                                <div class="menu-chevron">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                            <div class="menu-item" data-action="import">
                                <div class="menu-icon orange">
                                    <i class="fas fa-upload"></i>
                                </div>
                                <div class="menu-content">
                                    <div class="menu-title">导入数据</div>
                                    <div class="menu-subtitle">从文件恢复</div>
                                </div>
                                <div class="menu-chevron">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 应用设置 -->
                    <div class="menu-section">
                        <div class="section-title">应用设置</div>
                        <div class="menu-list">
                            <div class="menu-item" data-action="notifications">
                                <div class="menu-icon purple">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div class="menu-content">
                                    <div class="menu-title">通知设置</div>
                                    <div class="menu-subtitle">提醒和通知</div>
                                </div>
                                <div class="menu-chevron">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                            <div class="menu-item" data-action="privacy">
                                <div class="menu-icon blue">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="menu-content">
                                    <div class="menu-title">隐私安全</div>
                                    <div class="menu-subtitle">Face ID、密码保护</div>
                                </div>
                                <div class="menu-chevron">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                            <div class="menu-item" data-action="appearance">
                                <div class="menu-icon gray">
                                    <i class="fas fa-palette"></i>
                                </div>
                                <div class="menu-content">
                                    <div class="menu-title">外观设置</div>
                                    <div class="menu-subtitle">深色模式、主题色彩</div>
                                </div>
                                <div class="menu-chevron">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 帮助支持 -->
                    <div class="menu-section">
                        <div class="section-title">帮助支持</div>
                        <div class="menu-list">
                            <div class="menu-item" data-action="help">
                                <div class="menu-icon blue">
                                    <i class="fas fa-question-circle"></i>
                                </div>
                                <div class="menu-content">
                                    <div class="menu-title">使用帮助</div>
                                    <div class="menu-subtitle">功能介绍和使用指南</div>
                                </div>
                                <div class="menu-chevron">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                            <div class="menu-item" data-action="feedback">
                                <div class="menu-icon orange">
                                    <i class="fas fa-comment"></i>
                                </div>
                                <div class="menu-content">
                                    <div class="menu-title">意见反馈</div>
                                    <div class="menu-subtitle">问题报告和建议</div>
                                </div>
                                <div class="menu-badge">2</div>
                                <div class="menu-chevron">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                            <div class="menu-item" data-action="about">
                                <div class="menu-icon gray">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="menu-content">
                                    <div class="menu-title">关于应用</div>
                                    <div class="menu-subtitle">版本信息和开发团队</div>
                                </div>
                                <div class="menu-chevron">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 版本信息 -->
                    <div class="version-info">
                        棋牌记事本 v1.0.0<br>
                        © 2024 Chess Notebook App
                    </div>
                </div>

                <!-- 标签栏 -->
                <div class="tab-bar">
                    <div class="tab-item" data-page="home">
                        <div class="tab-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="tab-label">首页</div>
                    </div>
                    <div class="tab-item" data-page="records">
                        <div class="tab-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="tab-label">记录</div>
                    </div>
                    <div class="tab-item" data-page="stats">
                        <div class="tab-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="tab-label">统计</div>
                    </div>
                    <div class="tab-item" data-page="knowledge">
                        <div class="tab-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="tab-label">技巧</div>
                    </div>
                    <div class="tab-item active" data-page="profile">
                        <div class="tab-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="tab-label">我的</div>
                    </div>
                </div>

                <!-- Home Indicator -->
                <div class="home-indicator"></div>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script>
        // 个人中心页面特定功能
        document.addEventListener('DOMContentLoaded', function() {
            // 切换开关功能
            document.querySelectorAll('.toggle-switch').forEach(toggle => {
                toggle.addEventListener('click', function() {
                    this.classList.toggle('active');
                });
            });

            // 菜单项点击
            document.querySelectorAll('.menu-item').forEach(item => {
                item.addEventListener('click', function() {
                    const action = this.dataset.action;
                    console.log('菜单操作:', action);
                    
                    // 这里可以添加具体的菜单操作逻辑
                    switch(action) {
                        case 'backup-settings':
                            console.log('打开备份设置');
                            break;
                        case 'export':
                            console.log('导出数据');
                            break;
                        case 'import':
                            console.log('导入数据');
                            break;
                        // 其他菜单项...
                    }
                });
            });
        });
    </script>
</body>
</html>
