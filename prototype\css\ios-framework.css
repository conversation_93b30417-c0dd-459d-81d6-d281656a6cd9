/* iOS Framework CSS - iPhone 15 Pro 适配 */

:root {
  /* iPhone 15 Pro 尺寸 */
  --device-width: 393px;
  --device-height: 852px;
  --status-bar-height: 54px;
  --nav-bar-height: 44px;
  --tab-bar-height: 83px;
  --home-indicator-height: 34px;
  --safe-area-top: 54px;
  --safe-area-bottom: 34px;
  
  /* 品牌色彩 */
  --primary-color: #1E3A8A;
  --secondary-color: #F59E0B;
  --background-color: #F8FAFC;
  --surface-color: #FFFFFF;
  --text-primary: #374151;
  --text-secondary: #6B7280;
  --text-tertiary: #9CA3AF;
  --border-color: #E5E7EB;
  --success-color: #10B981;
  --error-color: #EF4444;
  --warning-color: #F59E0B;
  
  /* iOS 系统色彩 */
  --ios-blue: #007AFF;
  --ios-red: #FF3B30;
  --ios-green: #34C759;
  --ios-orange: #FF9500;
  --ios-purple: #AF52DE;
  --ios-gray: #8E8E93;
  --ios-gray2: #AEAEB2;
  --ios-gray3: #C7C7CC;
  --ios-gray4: #D1D1D6;
  --ios-gray5: #E5E5EA;
  --ios-gray6: #F2F2F7;
  
  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-large: 34px;
  --font-size-title1: 28px;
  --font-size-title2: 22px;
  --font-size-title3: 20px;
  --font-size-headline: 17px;
  --font-size-body: 17px;
  --font-size-callout: 16px;
  --font-size-subhead: 15px;
  --font-size-footnote: 13px;
  --font-size-caption1: 12px;
  --font-size-caption2: 11px;
  
  /* 圆角 */
  --border-radius-small: 8px;
  --border-radius-medium: 12px;
  --border-radius-large: 16px;
  --border-radius-xlarge: 20px;
  
  /* 阴影 */
  --shadow-small: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-large: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* 动画 */
  --animation-fast: 0.2s;
  --animation-normal: 0.3s;
  --animation-slow: 0.5s;
}

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background-color: var(--background-color);
  color: var(--text-primary);
  overflow: hidden;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* 设备容器 */
.device-container {
  width: var(--device-width);
  height: var(--device-height);
  margin: 20px auto;
  background: #000;
  border-radius: 40px;
  padding: 8px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
}

.device-screen {
  width: 100%;
  height: 100%;
  background: var(--background-color);
  border-radius: 32px;
  overflow: hidden;
  position: relative;
}

/* 状态栏 */
.status-bar {
  height: var(--status-bar-height);
  background: var(--surface-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  font-size: var(--font-size-footnote);
  font-weight: 600;
  position: relative;
  z-index: 1000;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-weight: 600;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 导航栏 */
.nav-bar {
  height: var(--nav-bar-height);
  background: var(--surface-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 0.5px solid var(--border-color);
  position: relative;
  z-index: 999;
}

.nav-title {
  font-size: var(--font-size-headline);
  font-weight: 600;
  color: var(--text-primary);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.nav-button {
  background: none;
  border: none;
  color: var(--ios-blue);
  font-size: var(--font-size-body);
  font-weight: 400;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color var(--animation-fast);
}

.nav-button:hover {
  background-color: var(--ios-gray6);
}

.nav-button.primary {
  font-weight: 600;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  overflow-y: auto;
  background: var(--background-color);
  padding-bottom: var(--safe-area-bottom);
}

/* 标签栏 */
.tab-bar {
  height: var(--tab-bar-height);
  background: var(--surface-color);
  border-top: 0.5px solid var(--border-color);
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  padding-top: 8px;
  padding-bottom: var(--safe-area-bottom);
  position: relative;
  z-index: 999;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all var(--animation-fast);
  padding: 4px 8px;
  border-radius: 8px;
}

.tab-item.active {
  color: var(--primary-color);
}

.tab-item:not(.active) {
  color: var(--ios-gray);
}

.tab-icon {
  font-size: 24px;
}

.tab-label {
  font-size: var(--font-size-caption1);
  font-weight: 500;
}

/* Home Indicator */
.home-indicator {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 134px;
  height: 5px;
  background: #000;
  border-radius: 3px;
  opacity: 0.3;
}

/* 页面布局 */
.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--background-color);
}

.page.hidden {
  display: none;
}

/* 列表样式 */
.list-container {
  background: var(--surface-color);
  border-radius: var(--border-radius-medium);
  margin: 16px;
  overflow: hidden;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 0.5px solid var(--border-color);
  cursor: pointer;
  transition: background-color var(--animation-fast);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:hover {
  background-color: var(--ios-gray6);
}

.list-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.list-item-title {
  font-size: var(--font-size-body);
  font-weight: 500;
  color: var(--text-primary);
}

.list-item-subtitle {
  font-size: var(--font-size-subhead);
  color: var(--text-secondary);
}

.list-item-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  color: var(--primary-color);
}

.list-item-chevron {
  color: var(--ios-gray2);
  font-size: 16px;
}

/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border-radius: var(--border-radius-medium);
  border: none;
  font-size: var(--font-size-body);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--animation-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #1E40AF;
}

.btn-secondary {
  background: var(--ios-gray6);
  color: var(--text-primary);
}

.btn-secondary:hover {
  background: var(--ios-gray5);
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-danger {
  background: var(--error-color);
  color: white;
}

/* 卡片样式 */
.card {
  background: var(--surface-color);
  border-radius: var(--border-radius-medium);
  padding: 16px;
  margin: 16px;
  box-shadow: var(--shadow-small);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-title {
  font-size: var(--font-size-headline);
  font-weight: 600;
  color: var(--text-primary);
}

.card-content {
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 表单样式 */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: var(--font-size-subhead);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-body);
  background: var(--surface-color);
  color: var(--text-primary);
  transition: border-color var(--animation-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }

.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { justify-content: center; align-items: center; }
.flex-between { justify-content: space-between; }
.flex-1 { flex: 1; }

.hidden { display: none; }
.visible { display: block; }

/* 响应式 */
@media (max-width: 414px) {
  .device-container {
    width: 100vw;
    height: 100vh;
    margin: 0;
    border-radius: 0;
    padding: 0;
    box-shadow: none;
  }
  
  .device-screen {
    border-radius: 0;
  }
}
