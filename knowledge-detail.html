<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技巧详情</title>
    <link rel="stylesheet" href="ios-framework.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .detail-header {
            background: linear-gradient(135deg, var(--primary-color), var(--ios-blue));
            color: white;
            padding: var(--spacing-lg) var(--spacing-md);
            margin: 0 var(--spacing-md) var(--spacing-md);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
        }

        .detail-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
            line-height: 1.3;
        }

        .detail-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .game-type-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            background: rgba(255, 255, 255, 0.2);
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .difficulty-display {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 14px;
        }

        .category-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .content-section {
            background: white;
            margin: 0 var(--spacing-md) var(--spacing-md);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            border: 0.5px solid var(--ios-gray5);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .content-text {
            font-size: 16px;
            line-height: 1.6;
            color: var(--text-primary);
            white-space: pre-wrap;
        }

        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
        }

        .tag-item {
            background: var(--ios-blue);
            color: white;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .action-buttons {
            display: flex;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
        }

        .action-button {
            flex: 1;
            padding: var(--spacing-md);
            border-radius: var(--radius-lg);
            border: none;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
        }

        .action-button.primary {
            background: var(--ios-blue);
            color: white;
        }

        .action-button.secondary {
            background: var(--ios-gray6);
            color: var(--text-primary);
        }

        .action-button.danger {
            background: var(--ios-red);
            color: white;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .favorite-section {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-lg);
        }

        .favorite-toggle {
            background: none;
            border: 2px solid var(--ios-red);
            color: var(--ios-red);
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--radius-lg);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .favorite-toggle.active {
            background: var(--ios-red);
            color: white;
        }

        .meta-info {
            background: var(--ios-gray6);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-top: var(--spacing-md);
        }

        .meta-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-xs) 0;
            font-size: 14px;
        }

        .meta-label {
            color: var(--text-secondary);
        }

        .meta-value {
            color: var(--text-primary);
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="status-time">14:30</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                        <div class="signal-bar"></div>
                    </div>
                    <div class="wifi-icon"></div>
                    <span>100%</span>
                    <div class="battery-icon"></div>
                </div>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <button class="nav-button" onclick="Navigator.back()">
                    <i class="fas fa-chevron-left"></i> 返回
                </button>
                <div class="nav-title">技巧详情</div>
                <div class="nav-buttons">
                    <button class="nav-button" onclick="editKnowledge()" id="editButton">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="nav-button" onclick="showMoreOptions()" id="moreButton">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
            </div>

            <!-- 主内容 -->
            <div class="main-content" id="mainContent">
                <!-- 动态加载内容 -->
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script src="app-utils.js"></script>
    <script>
        let currentKnowledge = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadKnowledgeDetail();
        });

        // 加载技巧详情
        function loadKnowledgeDetail() {
            const params = Navigator.getParams();
            const knowledgeId = params.id;

            if (!knowledgeId) {
                UIUtils.showToast('技巧不存在', 'error');
                Navigator.back();
                return;
            }

            const knowledge = appStorage.get('knowledge') || [];
            currentKnowledge = knowledge.find(k => k.id === knowledgeId);

            if (!currentKnowledge) {
                UIUtils.showToast('技巧不存在', 'error');
                Navigator.back();
                return;
            }

            renderKnowledgeDetail();
        }

        // 渲染技巧详情
        function renderKnowledgeDetail() {
            const container = document.getElementById('mainContent');
            const gameTypeInfo = GAME_TYPES[currentKnowledge.gameType] || { icon: '🎯', color: '#666' };

            container.innerHTML = `
                <!-- 详情头部 -->
                <div class="detail-header">
                    <div class="detail-title">${currentKnowledge.title}</div>
                    <div class="detail-meta">
                        <div class="game-type-badge">
                            <span>${gameTypeInfo.icon}</span>
                            <span>${currentKnowledge.gameType}</span>
                        </div>
                        <div class="difficulty-display">
                            <span>难度:</span>
                            <span>${'⭐'.repeat(currentKnowledge.difficulty || 1)}</span>
                        </div>
                    </div>
                    <div class="category-badge">${currentKnowledge.category}</div>
                </div>

                <!-- 内容区域 -->
                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-file-text"></i>
                        详细内容
                    </div>
                    <div class="content-text">${currentKnowledge.content}</div>
                    
                    <div class="meta-info">
                        <div class="meta-item">
                            <span class="meta-label">创建时间</span>
                            <span class="meta-value">${DateUtils.formatDate(currentKnowledge.createdAt)}</span>
                        </div>
                        ${currentKnowledge.updatedAt ? `
                            <div class="meta-item">
                                <span class="meta-label">更新时间</span>
                                <span class="meta-value">${DateUtils.formatDate(currentKnowledge.updatedAt)}</span>
                            </div>
                        ` : ''}
                    </div>
                </div>

                <!-- 标签区域 -->
                ${currentKnowledge.tags && currentKnowledge.tags.length > 0 ? `
                    <div class="content-section">
                        <div class="section-title">
                            <i class="fas fa-tags"></i>
                            标签
                        </div>
                        <div class="tags-container">
                            ${currentKnowledge.tags.map(tag => `<span class="tag-item">${tag}</span>`).join('')}
                        </div>
                    </div>
                ` : ''}

                <!-- 收藏按钮 -->
                <div class="favorite-section">
                    <button class="favorite-toggle ${currentKnowledge.isFavorite ? 'active' : ''}" onclick="toggleFavorite()">
                        <i class="fas fa-heart"></i>
                        ${currentKnowledge.isFavorite ? '已收藏' : '收藏'}
                    </button>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="action-button secondary" onclick="editKnowledge()">
                        <i class="fas fa-edit"></i>
                        编辑
                    </button>
                    <button class="action-button danger" onclick="deleteKnowledge()">
                        <i class="fas fa-trash"></i>
                        删除
                    </button>
                </div>
            `;
        }

        // 切换收藏状态
        function toggleFavorite() {
            const knowledge = appStorage.get('knowledge') || [];
            const item = knowledge.find(k => k.id === currentKnowledge.id);

            if (item) {
                item.isFavorite = !item.isFavorite;
                currentKnowledge.isFavorite = item.isFavorite;
                appStorage.set('knowledge', knowledge);

                // 更新按钮状态
                const button = document.querySelector('.favorite-toggle');
                button.classList.toggle('active', item.isFavorite);
                button.innerHTML = `
                    <i class="fas fa-heart"></i>
                    ${item.isFavorite ? '已收藏' : '收藏'}
                `;

                UIUtils.showToast(item.isFavorite ? '已添加到收藏' : '已取消收藏', 'success');
            }
        }

        // 编辑技巧
        function editKnowledge() {
            Navigator.navigate('add-knowledge.html', {
                edit: 'true',
                id: currentKnowledge.id
            });
        }

        // 删除技巧
        function deleteKnowledge() {
            UIUtils.showDialog('确认删除', '确定要删除这个技巧吗？删除后无法恢复。', [
                {
                    text: '取消',
                    action: () => {}
                },
                {
                    text: '删除',
                    action: () => {
                        const knowledge = appStorage.get('knowledge') || [];
                        const updatedKnowledge = knowledge.filter(k => k.id !== currentKnowledge.id);
                        appStorage.set('knowledge', updatedKnowledge);

                        UIUtils.showToast('技巧已删除', 'success');
                        setTimeout(() => {
                            Navigator.navigate('knowledge.html');
                        }, 1000);
                    }
                }
            ]);
        }

        // 显示更多选项
        function showMoreOptions() {
            const options = [
                {
                    text: '复制内容',
                    icon: 'fas fa-copy',
                    action: () => copyContent()
                },
                {
                    text: '应用到记录',
                    icon: 'fas fa-plus-circle',
                    action: () => applyToRecord()
                }
            ];

            UIUtils.showActionSheet('更多操作', options);
        }

        // 复制内容
        function copyContent() {
            const content = `${currentKnowledge.title}\n\n${currentKnowledge.content}\n\n标签: ${currentKnowledge.tags.join(', ')}`;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(content).then(() => {
                    UIUtils.showToast('内容已复制', 'success');
                }).catch(() => {
                    fallbackCopy(content);
                });
            } else {
                fallbackCopy(content);
            }
        }

        // 备用复制方法
        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                UIUtils.showToast('内容已复制', 'success');
            } catch (err) {
                UIUtils.showToast('复制失败', 'error');
            }
            document.body.removeChild(textArea);
        }

        // 应用到记录
        function applyToRecord() {
            Navigator.navigate('add-record.html', {
                gameType: currentKnowledge.gameType,
                notes: `参考技巧: ${currentKnowledge.title}\n\n${currentKnowledge.content}`,
                tags: currentKnowledge.tags.join(',')
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                Navigator.back();
            } else if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
                e.preventDefault();
                editKnowledge();
            } else if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
                e.preventDefault();
                copyContent();
            }
        });
    </script>
</body>
</html>
